package com.tieredchests.listeners

import com.tieredchests.TieredChestsPlugin
import com.tieredchests.gui.LootTableSelectionGUI
import org.bukkit.NamespacedKey
import org.bukkit.event.EventHandler
import org.bukkit.event.EventPriority
import org.bukkit.event.Listener
import org.bukkit.event.block.BlockPlaceEvent
import org.bukkit.persistence.PersistentDataType

class ChestPlaceListener(private val plugin: TieredChestsPlugin) : Listener {
    
    private val chestItemKey = NamespacedKey(plugin, "chest_item")
    private val tierIdKey = NamespacedKey(plugin, "tier_id")
    
    @EventHandler(priority = EventPriority.HIGH, ignoreCancelled = true)
    fun onPlace(event: BlockPlaceEvent) {
        val item = event.itemInHand
        if (!item.hasItemMeta()) return
        
        val meta = item.itemMeta
        val container = meta.persistentDataContainer
        
        if (!container.has(chestItemKey, PersistentDataType.BOOLEAN)) return
        
        val tierId = container.get(tierIdKey, PersistentDataType.STRING) ?: return
        val tier = plugin.tierManager.getTier(tierId)
        
        if (tier == null) {
            event.player.sendMessage("§cInvalid tier: $tierId")
            event.isCancelled = true
            return
        }
        
        // Check permission
        if (!event.player.hasPermission("ctc.place")) {
            event.player.sendMessage("§cYou don't have permission to place tiered chests!")
            event.isCancelled = true
            return
        }
        
        // Set the block to the tier's core block
        event.blockPlaced.type = tier.coreBlock
        
        // Open loot table selection GUI
        plugin.server.scheduler.runTaskLater(plugin, Runnable {
            LootTableSelectionGUI(plugin, event.player, event.blockPlaced.location, tierId).open()
        }, 1L)
    }
}

