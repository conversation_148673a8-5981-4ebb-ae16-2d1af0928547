package com.tieredchests.listeners

import com.tieredchests.TieredChestsPlugin
import com.tieredchests.gui.LootTableSelectionGUI
import org.bukkit.NamespacedKey
import org.bukkit.event.EventHandler
import org.bukkit.event.EventPriority
import org.bukkit.event.Listener
import org.bukkit.event.block.BlockPlaceEvent
import org.bukkit.persistence.PersistentDataType

class ChestPlaceListener(private val plugin: TieredChestsPlugin) : Listener {
    
    private val chestItemKey = NamespacedKey(plugin, "chest_item")
    private val tierIdKey = NamespacedKey(plugin, "tier_id")
    
    @EventHandler(priority = EventPriority.HIGH, ignoreCancelled = true)
    fun onPlace(event: BlockPlaceEvent) {
        val item = event.itemInHand
        if (!item.hasItemMeta()) return

        val meta = item.itemMeta
        val container = meta.persistentDataContainer

        if (!container.has(chestItemKey, PersistentDataType.BOOLEAN)) return

        plugin.logger.info("Player ${event.player.name} is placing a tiered chest")

        val tierId = container.get(tierIdKey, PersistentDataType.STRING) ?: return
        val tier = plugin.tierManager.getTier(tierId)

        if (tier == null) {
            event.player.sendMessage("§cInvalid tier: $tierId")
            event.isCancelled = true
            plugin.logger.warning("Invalid tier: $tierId")
            return
        }

        // Check permission
        if (!event.player.hasPermission("ctc.place")) {
            event.player.sendMessage("§cYou don't have permission to place tiered chests!")
            event.isCancelled = true
            plugin.logger.info("Player ${event.player.name} lacks ctc.place permission")
            return
        }

        plugin.logger.info("Setting block to ${tier.coreBlock} and opening GUI")

        // Set the block to the tier's core block
        event.blockPlaced.type = tier.coreBlock

        // Open loot table selection GUI on the main thread
        plugin.server.scheduler.runTaskLater(plugin, Runnable {
            try {
                LootTableSelectionGUI(plugin, event.player, event.blockPlaced.location, tierId).open()
            } catch (e: Exception) {
                plugin.logger.severe("Failed to open GUI: ${e.message}")
                e.printStackTrace()
                event.player.sendMessage("§cFailed to open loot table selection GUI!")
            }
        }, 1L)
    }
}

