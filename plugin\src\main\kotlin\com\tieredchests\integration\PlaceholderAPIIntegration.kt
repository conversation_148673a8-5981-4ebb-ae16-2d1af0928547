package com.tieredchests.integration

import com.tieredchests.TieredChestsPlugin
import me.clip.placeholderapi.expansion.PlaceholderExpansion
import org.bukkit.entity.Player

class PlaceholderAPIIntegration(private val plugin: TieredChestsPlugin) : PlaceholderExpansion() {
    
    override fun getIdentifier(): String = "ctc"
    
    override fun getAuthor(): String = "TieredChests"
    
    override fun getVersion(): String = plugin.description.version
    
    override fun persist(): Boolean = true
    
    override fun onPlaceholderRequest(player: Player?, params: String): String? {
        if (player == null) return null
        
        // %ctc_tier% - Get tier of nearest chest
        // %ctc_ready_in% - Get cooldown of nearest chest
        
        return when (params.lowercase()) {
            "tier" -> {
                // Find nearest chest
                val nearestChest = plugin.chestManager.getAllChests()
                    .filter { it.location.world == player.world }
                    .minByOrNull { it.location.distanceSquared(player.location) }
                
                nearestChest?.tierId ?: "none"
            }
            "ready_in" -> {
                val nearestChest = plugin.chestManager.getAllChests()
                    .filter { it.location.world == player.world }
                    .minByOrNull { it.location.distanceSquared(player.location) }
                
                if (nearestChest == null) {
                    "none"
                } else if (nearestChest.isReady()) {
                    "ready"
                } else {
                    val remaining = nearestChest.getRemainingCooldown()
                    val minutes = remaining / 60000
                    val seconds = (remaining % 60000) / 1000
                    "${minutes}m ${seconds}s"
                }
            }
            else -> null
        }
    }
}

