# Custom Tiered Chests

A production-ready Minecraft plugin for Paper/Purpur 1.20.4+ that creates visually distinct tiered chests using **Display Entities** and **vanilla assets only** - no resource pack required!

## Features

### 🎨 Visual Design (No Resource Pack!)
- **Display Entity-based visuals** using vanilla blocks and items
- Each tier has unique decorations:
  - **Minecart** items as metallic plates
  - **Rails** as decorative bands
  - **Chains** for corner accents
  - **Amethyst clusters** as gems
  - **Trapdoors** as veneers
  - **Netherite scraps** as studs
  - **Obsidian** insets
- Floating text badges (toggleable)
- Tier-specific particles and sounds

### 🔐 Key System
- **Tier-based keys**: Work on any chest of that tier
- **Instance-based keys**: Bound to specific chest
- **Configurable consumption**: Never, on open, or on first open
- **Limited uses**: Optional use counter

### 🎁 Loot System
- **GUI-based loot table selection** on placement
- Support for **vanilla loot tables** (e.g., `minecraft:chests/end_city_treasure`)
- **Custom loot tables** with weighted pools
- Per-chest loot table persistence

### ⏰ Respawn System
- **Fixed cooldowns** (e.g., 60 minutes)
- **Cron expressions** for scheduled respawns
- Visual cooldown indicators
- Per-tier respawn policies

### 🌍 Folia Support
- **Region-thread safe** operations
- Async I/O for all file operations
- No blocking on server threads
- Fully compatible with Paper/Purpur

### 🔌 Integrations
- **PlaceholderAPI**: `%ctc_tier%`, `%ctc_ready_in%`
- **WorldGuard**: Region protection checks (optional)
- **Vault**: Economy integration (optional)

## Quick Start

### Installation

1. Download the latest release JAR
2. Place in your server's `plugins/` folder
3. Restart the server
4. Configure `tiers.yml` and `loot_tables.yml` to your liking

### Basic Usage

```bash
# Give yourself a chest
/ctc givechest <player> <tier> [amount]

# Give yourself a key
/ctc givekey <player> <tier> [uses] [bind:tier|instance]

# Place the chest item
# Select a loot table from the GUI
# Right-click with a key to open
```

## Default Tiers

| Tier | Core Block | Lock Mode | Respawn | Visual Theme |
|------|-----------|-----------|---------|--------------|
| **Common** | Barrel | Tier | 60 min | Minecart plates + chains |
| **Rare** | Barrel | Tier | 120 min | Rail bands + iron studs |
| **Epic** | Barrel | Tier | 240 min | Amethyst gems + gold accents |
| **Mythic** | Shulker Box | Instance | 480 min | Obsidian core + netherite |

## Requirements

- **Java 17+**
- **Paper/Purpur 1.20.4 - 1.21.x**
- **Folia-compatible** (optional)
- **No client mods or resource packs required!**

## Building from Source

```bash
# Clone the repository
git clone https://github.com/yourusername/custom-tiered-chests.git
cd custom-tiered-chests

# Build with Maven
C:\Users\<USER>\Downloads\apache-maven-3.9.11\bin\mvn clean package

# Output: plugin/target/custom-tiered-chests-plugin-1.0.0.jar
```

## Documentation

- [Configuration Guide](CONFIG.md)
- [Commands & Permissions](COMMANDS.md)
- [Loot Tables](LOOT_TABLES.md)
- [Permissions](PERMISSIONS.md)

## API Usage

```java
// Get the API
TieredChestsAPI api = TieredChestsPlugin.getInstance().getApi();

// Get a chest
ChestInstance chest = api.getChest(location);

// Give a key
api.giveKey(player, "mythic", 3, KeyBindMode.TIER);

// Register custom tier
TierDefinition customTier = new TierDefinition(...);
api.registerTier(customTier);
```

## Support

- **Issues**: [GitHub Issues](https://github.com/yourusername/custom-tiered-chests/issues)
- **Wiki**: [GitHub Wiki](https://github.com/yourusername/custom-tiered-chests/wiki)

## License

MIT License - See LICENSE file for details

## Credits

- Built with **Paper API**
- Uses **Display Entities** (1.20+)
- Kotlin + Maven
- Folia-safe design

