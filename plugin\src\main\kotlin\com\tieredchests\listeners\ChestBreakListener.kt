package com.tieredchests.listeners

import com.tieredchests.TieredChestsPlugin
import org.bukkit.event.EventHandler
import org.bukkit.event.EventPriority
import org.bukkit.event.Listener
import org.bukkit.event.block.BlockBreakEvent

class ChestBreakListener(private val plugin: TieredChestsPlugin) : Listener {
    
    @EventHandler(priority = EventPriority.HIGH, ignoreCancelled = true)
    fun onBreak(event: BlockBreakEvent) {
        val chest = plugin.chestManager.getChestAt(event.block.location) ?: return
        
        // Check permission
        if (!event.player.hasPermission("ctc.break")) {
            event.player.sendMessage("§cYou don't have permission to break tiered chests!")
            event.isCancelled = true
            return
        }
        
        // Check ownership (optional)
        if (chest.owner != null && chest.owner != event.player.uniqueId) {
            if (!event.player.hasPermission("ctc.admin.break")) {
                event.player.sendMessage("§cYou don't own this chest!")
                event.isCancelled = true
                return
            }
        }
        
        // Remove the chest
        plugin.chestManager.removeChest(chest)
        event.player.sendMessage("§aTiered chest removed!")
    }
}

