# Configuration Guide

## config.yml

```yaml
configVersion: 1

settings:
  debug: false                    # Enable debug logging
  autoSaveInterval: 5             # Auto-save interval in minutes
  maxDisplayEntities: 20          # Max display entities per chest
  enableParticles: true           # Enable particle effects
  enableSounds: true              # Enable sound effects

integrations:
  worldguard:
    enabled: true
    checkPlacement: true          # Check regions on placement
    checkOpening: true            # Check regions on opening
  
  vault:
    enabled: false
    openCost: 0.0                 # Cost to open chests
  
  placeholderapi:
    enabled: true
```

## tiers.yml

Define custom tiers with unique visuals:

```yaml
configVersion: 1

tiers:
  custom_tier:
    displayName: "&6Custom Chest"
    colorName: GOLD
    coreBlock: BARREL             # The actual container block
    lockMode: TIER                # NONE, TIER, or INSTANCE
    keyConsumeMode: ON_OPEN       # NEVER, ON_OPEN, or ON_FIRST_OPEN
    accentScheme: CustomScheme
    
    # Visual accents using Display Entities
    accents:
      decorations:
        - type: ITEM_DISPLAY
          item: MINECART
          transform:
            sx: 0.9               # Scale X
            sy: 0.1               # Scale Y
            sz: 0.6               # Scale Z
            rx: -90               # Rotation X (degrees)
            ry: 0                 # Rotation Y
            rz: 0                 # Rotation Z
            tx: 0.0               # Translation X
            ty: 0.65              # Translation Y
            tz: 0.0               # Translation Z
        
        - type: BLOCK_DISPLAY
          block: CHAIN
          transform:
            sx: 0.15
            sy: 0.9
            sz: 0.15
            rx: 0
            ry: 0
            rz: 0
            tx: -0.45
            ty: 0.2
            tz: -0.45
    
    # Text badge above chest
    textBadge:
      enabled: true
      content: "&6CUSTOM"
      offset:
        x: 0.0
        y: 1.05
        z: -0.51
      scale: 0.5
      billboard: CENTER           # FIXED, VERTICAL, HORIZONTAL, CENTER
    
    # Sound effects
    openSound: BLOCK_CHEST_OPEN
    closeSound: BLOCK_CHEST_CLOSE
    
    # Particle effects
    particles:
      particle: VILLAGER_HAPPY
      count: 5
      offsetX: 0.5
      offsetY: 0.5
      offsetZ: 0.5
      speed: 0.1
    
    # Respawn policy
    respawnPolicy:
      type: FIXED                 # NONE, FIXED, or CRON
      minutes: 60                 # For FIXED type
      # expression: "0 0 * * *"   # For CRON type
```

### Display Types

- **BLOCK_DISPLAY**: Use vanilla blocks (e.g., CHAIN, RAIL, AMETHYST_CLUSTER)
- **ITEM_DISPLAY**: Use items (e.g., MINECART, GOLD_INGOT, NETHERITE_SCRAP)
- **TEXT_DISPLAY**: Floating text (handled by textBadge)

### Transform Parameters

- **Scale (sx, sy, sz)**: Size multiplier (1.0 = normal size)
- **Rotation (rx, ry, rz)**: Rotation in degrees
- **Translation (tx, ty, tz)**: Position offset in blocks

### Lock Modes

- **NONE**: No key required
- **TIER**: Any key for this tier works
- **INSTANCE**: Key must be bound to this specific chest

### Key Consume Modes

- **NEVER**: Key is never consumed
- **ON_OPEN**: Key consumed every time
- **ON_FIRST_OPEN**: Key consumed only on first open

### Respawn Policies

- **NONE**: No respawn, one-time loot
- **FIXED**: Respawn after X minutes
- **CRON**: Respawn on schedule (e.g., daily at midnight)

## loot_tables.yml

Define custom loot pools:

```yaml
configVersion: 1

tables:
  custom_loot:
    rolls: 3                      # Number of items to roll
    pools:
      - weight: 50                # Relative weight
        item: "minecraft:diamond"
        min: 1                    # Minimum amount
        max: 3                    # Maximum amount
      
      - weight: 30
        item: "minecraft:emerald"
        min: 2
        max: 5
      
      - weight: 20
        item: "minecraft:netherite_ingot"
        min: 1
        max: 1
```

### Using Vanilla Loot Tables

You can also reference vanilla loot tables:

```yaml
# In the GUI or via command
minecraft:chests/simple_dungeon
minecraft:chests/end_city_treasure
minecraft:chests/nether_bridge
minecraft:chests/buried_treasure
```

## Per-Instance Storage

Chest instances are stored in `chests/<UUID>.yml`:

```yaml
uuid: "123e4567-e89b-12d3-a456-************"
world: "world"
x: 100.0
y: 64.0
z: 200.0
tierId: "mythic"
lootTableId: "mythic_end"
owner: "player-uuid"
placedAt: 1234567890000
lastOpened: 1234567890000
nextRespawn: 1234567890000
firstOpenDone: true
shellEntities:
  - "entity-uuid-1"
  - "entity-uuid-2"
badgeEnabled: true
```

## Tips

1. **Start small**: Begin with 3-5 accent pieces per tier
2. **Test transforms**: Use `/ctc rebuild` to test changes
3. **Performance**: Keep `maxDisplayEntities` under 25
4. **Backup**: Always backup before major config changes
5. **Reload**: Use `/ctc reload` to apply changes without restart

## Visual Design Tips

### Creating Unique Silhouettes

1. **Vary scale**: Use different sizes for each tier
2. **Rotate items**: 45° rotations create diamond shapes
3. **Layer pieces**: Stack displays for depth
4. **Use color**: Amethyst (purple), chains (gray), gold (yellow)

### Recommended Vanilla Assets

- **Metallic**: CHAIN, IRON_BARS, ANVIL
- **Decorative**: RAIL, TRAPDOOR, BUTTON
- **Gems**: AMETHYST_CLUSTER, AMETHYST_BUD
- **Items**: MINECART, GOLD_INGOT, NETHERITE_SCRAP, EMERALD
- **Core**: OBSIDIAN, POLISHED_BLACKSTONE

### Example Accent Schemes

**CartPlate**: Minecart lid + chain corners
**RailBand**: Rail wraparound + button rivets  
**GemEdge**: Amethyst corners + trapdoor faces
**ObsidianCore**: Obsidian inset + netherite studs

