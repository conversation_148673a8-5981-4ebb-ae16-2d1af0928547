# Changelog

## [1.0.0] - 2025-10-03

### Fixed
- **Command Registration**: Removed permission requirement from plugin.yml that was blocking all non-op players from executing `/ctc` command
- **Tab Completion**: Added tab completer registration to enable command auto-completion
- **GUI Loot Table Selection**: Fixed unreliable text extraction by storing loot table IDs in NBT (PersistentDataContainer)
- **Error Handling**: Added comprehensive try-catch blocks and logging throughout command execution and GUI opening
- **Logging**: Added detailed logging to help debug issues with chest placement and GUI opening

### Changed
- Command permission checking now happens inside the command handler instead of plugin.yml
- GUI items now store loot table IDs in NBT for reliable retrieval
- Improved error messages for players when operations fail

### Added
- Debug logging for chest placement events
- Debug logging for GUI opening
- Debug logging for loot table selection
- Better error messages in console and for players

### Technical Details

#### Files Modified
1. **plugin/src/main/resources/plugin.yml**
   - Removed `permission: ctc.admin` from command definition
   - Commands now accessible to all players (permissions checked internally)

2. **plugin/src/main/kotlin/com/tieredchests/TieredChestsPlugin.kt**
   - Added tab completer registration
   - Changed from single-line to multi-line command registration

3. **plugin/src/main/kotlin/com/tieredchests/commands/CTCCommand.kt**
   - Added try-catch block around command execution
   - Added error logging and player feedback

4. **plugin/src/main/kotlin/com/tieredchests/gui/LootTableSelectionGUI.kt**
   - Added `PlainTextComponentSerializer` import
   - Added `NamespacedKey` for loot table ID storage
   - Changed loot table ID storage from display name to NBT
   - Added try-catch block in `open()` method
   - Improved loot table ID retrieval using NBT instead of text parsing
   - Added logging for GUI operations

5. **plugin/src/main/kotlin/com/tieredchests/listeners/ChestPlaceListener.kt**
   - Added logging for placement events
   - Added try-catch block around GUI opening
   - Added better error messages

### Migration Notes
- No configuration changes required
- No data migration needed
- Simply replace the old JAR with the new one and restart

### Known Issues
- None currently

### Upgrade Instructions

1. **Stop your server**
2. **Backup your data**:
   ```
   plugins/CustomTieredChests/
   ```
3. **Replace the JAR**:
   ```
   plugins/custom-tiered-chests-plugin-1.0.0.jar
   ```
4. **Start your server**
5. **Test the fixes**:
   - Run `/ctc` (should show help)
   - Run `/ctc givechest <player> common 1`
   - Place the chest (GUI should open)
   - Select a loot table

### Testing Checklist

- [ ] `/ctc` command shows help message
- [ ] Tab completion works for subcommands
- [ ] `/ctc givechest` gives a chest item
- [ ] Placing chest opens GUI
- [ ] GUI shows loot tables
- [ ] Clicking loot table creates chest
- [ ] `/ctc givekey` gives a key
- [ ] Opening chest with key generates loot
- [ ] Display entities appear around chest
- [ ] `/ctc reload` works
- [ ] `/ctc rebuild` works

### Performance
- No performance impact from these changes
- Logging can be disabled by setting `debug: false` in config.yml

### Compatibility
- Paper 1.20.4+
- Purpur 1.20.4+
- Folia (experimental)
- Java 17+

### Dependencies
- Kotlin 1.9.22 (shaded)
- Paper API 1.20.4-R0.1-SNAPSHOT
- PlaceholderAPI 2.11.5 (optional)
- WorldGuard 7.0.9 (optional)
- Vault 1.7 (optional)

---

## [1.0.0-beta] - 2025-10-02

### Added
- Initial release
- 4 default tiers (Common, Rare, Epic, Mythic)
- Display Entity visuals using vanilla assets
- Key-based access system
- Loot table system (custom + vanilla)
- Respawn/cooldown system
- GUI for loot table selection
- Full command suite
- API for developers
- Folia support
- PlaceholderAPI integration
- WorldGuard integration
- Vault integration
- Comprehensive documentation

### Features
- **No Resource Pack Required**: 100% server-side visuals
- **Vanilla Assets Only**: Uses minecarts, rails, chains, amethyst, etc.
- **Display Entities**: Block/Item/Text displays for unique looks
- **Tiered System**: Multiple tiers with distinct visuals
- **Key System**: Tier-based and instance-based keys
- **Loot System**: Custom weighted pools + vanilla loot tables
- **Respawn System**: Fixed cooldowns and cron expressions
- **GUI**: Loot table selection on placement
- **Commands**: Full admin command suite
- **Permissions**: Granular permission system
- **API**: Public API for developers
- **Integrations**: PlaceholderAPI, WorldGuard, Vault
- **Folia-Safe**: Region-thread safe operations

### Known Issues (Fixed in 1.0.0)
- Commands not working for non-op players
- Tab completion not working
- GUI loot table selection unreliable

