package com.tieredchests.listeners

import com.tieredchests.TieredChestsPlugin
import org.bukkit.event.EventHandler
import org.bukkit.event.Listener
import org.bukkit.event.inventory.InventoryClickEvent

class InventoryListener(private val plugin: TieredChestsPlugin) : Listener {
    
    @EventHandler
    fun onClick(event: InventoryClickEvent) {
        val title = event.view.title()
        // Handle GUI clicks - will be implemented in GUI classes
    }
}

