package com.tieredchests.manager

import com.tieredchests.TieredChestsPlugin
import com.tieredchests.model.KeyBindMode
import com.tieredchests.model.KeyData
import net.kyori.adventure.text.Component
import net.kyori.adventure.text.format.NamedTextColor
import net.kyori.adventure.text.format.TextDecoration
import org.bukkit.Material
import org.bukkit.NamespacedKey
import org.bukkit.inventory.ItemStack
import org.bukkit.persistence.PersistentDataType
import java.util.*

class KeyManager(private val plugin: TieredChestsPlugin) {
    
    private val keyKey = NamespacedKey(plugin, "key")
    private val tierIdKey = NamespacedKey(plugin, "tier_id")
    private val boundChestKey = NamespacedKey(plugin, "bound_chest")
    private val usesKey = NamespacedKey(plugin, "uses")
    private val bindModeKey = NamespacedKey(plugin, "bind_mode")
    
    fun createKey(tierId: String, uses: Int?, bindMode: KeyBindMode, boundChest: UUID?): ItemStack {
        val tier = plugin.tierManager.getTier(tierId) ?: throw IllegalArgumentException("Unknown tier: $tierId")
        
        val item = ItemStack(Material.TRIPWIRE_HOOK)
        val meta = item.itemMeta
        
        // Set display name
        val keyName = Component.text("${tier.displayName} Key")
            .color(NamedTextColor.GOLD)
            .decoration(TextDecoration.ITALIC, false)
        meta.displayName(keyName)
        
        // Set lore
        val lore = mutableListOf<Component>()
        lore.add(Component.text("Tier: ${tier.displayName}").color(NamedTextColor.GRAY))
        
        when (bindMode) {
            KeyBindMode.TIER -> lore.add(Component.text("Bind: Any ${tier.displayName} chest").color(NamedTextColor.YELLOW))
            KeyBindMode.INSTANCE -> {
                if (boundChest != null) {
                    lore.add(Component.text("Bind: Specific chest").color(NamedTextColor.YELLOW))
                    lore.add(Component.text("UUID: ${boundChest.toString().substring(0, 8)}...").color(NamedTextColor.DARK_GRAY))
                } else {
                    lore.add(Component.text("Bind: Next opened chest").color(NamedTextColor.YELLOW))
                }
            }
        }
        
        if (uses != null) {
            lore.add(Component.text("Uses: $uses").color(NamedTextColor.GREEN))
        } else {
            lore.add(Component.text("Uses: Unlimited").color(NamedTextColor.GREEN))
        }
        
        meta.lore(lore)
        
        // Set persistent data
        meta.persistentDataContainer.set(keyKey, PersistentDataType.BOOLEAN, true)
        meta.persistentDataContainer.set(tierIdKey, PersistentDataType.STRING, tierId)
        meta.persistentDataContainer.set(bindModeKey, PersistentDataType.STRING, bindMode.name)
        
        if (uses != null) {
            meta.persistentDataContainer.set(usesKey, PersistentDataType.INTEGER, uses)
        }
        
        if (boundChest != null) {
            meta.persistentDataContainer.set(boundChestKey, PersistentDataType.STRING, boundChest.toString())
        }
        
        item.itemMeta = meta
        return item
    }
    
    fun isKey(item: ItemStack?): Boolean {
        if (item == null || !item.hasItemMeta()) return false
        return item.itemMeta.persistentDataContainer.has(keyKey, PersistentDataType.BOOLEAN)
    }
    
    fun getKeyData(item: ItemStack): KeyData? {
        if (!isKey(item)) return null
        
        val meta = item.itemMeta
        val container = meta.persistentDataContainer
        
        val tierId = container.get(tierIdKey, PersistentDataType.STRING) ?: return null
        val bindMode = KeyBindMode.valueOf(container.get(bindModeKey, PersistentDataType.STRING) ?: "TIER")
        val uses = container.get(usesKey, PersistentDataType.INTEGER)
        val boundChestStr = container.get(boundChestKey, PersistentDataType.STRING)
        val boundChest = boundChestStr?.let { UUID.fromString(it) }
        
        return KeyData(tierId, boundChest, uses, bindMode)
    }
    
    fun consumeUse(item: ItemStack): Boolean {
        if (!isKey(item)) return false
        
        val meta = item.itemMeta
        val container = meta.persistentDataContainer
        
        val uses = container.get(usesKey, PersistentDataType.INTEGER) ?: return true // Unlimited
        
        if (uses <= 1) {
            return false // Key should be removed
        }
        
        val newUses = uses - 1
        container.set(usesKey, PersistentDataType.INTEGER, newUses)
        
        // Update lore
        val lore = meta.lore() ?: mutableListOf()
        val newLore = lore.map { line ->
            val text = (line as? Component)?.toString() ?: ""
            if (text.contains("Uses:")) {
                Component.text("Uses: $newUses").color(NamedTextColor.GREEN)
            } else {
                line
            }
        }
        meta.lore(newLore)
        
        item.itemMeta = meta
        return true
    }
    
    fun bindKey(item: ItemStack, chestUUID: UUID) {
        if (!isKey(item)) return
        
        val meta = item.itemMeta
        val container = meta.persistentDataContainer
        
        container.set(boundChestKey, PersistentDataType.STRING, chestUUID.toString())
        
        // Update lore
        val lore = meta.lore()?.toMutableList() ?: mutableListOf()
        lore.add(Component.text("Bound to: ${chestUUID.toString().substring(0, 8)}...").color(NamedTextColor.DARK_PURPLE))
        meta.lore(lore)
        
        item.itemMeta = meta
    }
}

