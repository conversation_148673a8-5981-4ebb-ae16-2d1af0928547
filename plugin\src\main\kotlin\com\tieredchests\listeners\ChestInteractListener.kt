package com.tieredchests.listeners

import com.tieredchests.TieredChestsPlugin
import com.tieredchests.event.ChestOpenEvent
import com.tieredchests.model.KeyConsumeMode
import com.tieredchests.model.LockMode
import org.bukkit.Particle
import org.bukkit.event.EventHandler
import org.bukkit.event.EventPriority
import org.bukkit.event.Listener
import org.bukkit.event.block.Action
import org.bukkit.event.player.PlayerInteractEvent
import org.bukkit.inventory.EquipmentSlot

class ChestInteractListener(private val plugin: TieredChestsPlugin) : Listener {
    
    @EventHandler(priority = EventPriority.HIGH, ignoreCancelled = true)
    fun onInteract(event: PlayerInteractEvent) {
        if (event.action != Action.RIGHT_CLICK_BLOCK) return
        if (event.hand != EquipmentSlot.HAND) return
        
        val block = event.clickedBlock ?: return
        val chest = plugin.chestManager.getChestAt(block.location) ?: return
        
        event.isCancelled = true
        
        val player = event.player
        val tier = plugin.tierManager.getTier(chest.tierId)
        
        if (tier == null) {
            player.sendMessage("§cInvalid chest tier!")
            return
        }
        
        // Check if chest is ready
        if (!chest.isReady()) {
            val remaining = chest.getRemainingCooldown()
            val minutes = remaining / 60000
            val seconds = (remaining % 60000) / 1000
            player.sendMessage("§eThis chest is recharging... ${minutes}m ${seconds}s remaining")
            plugin.shellManager.updateCooldownDisplay(chest, tier)
            return
        }
        
        // Check lock
        if (tier.lockMode != LockMode.NONE) {
            val itemInHand = player.inventory.itemInMainHand
            val keyData = plugin.keyManager.getKeyData(itemInHand)
            
            if (keyData == null) {
                player.sendMessage("§cYou need a ${tier.displayName} §ckey to open this chest!")
                return
            }
            
            // Validate key
            if (keyData.tierId != tier.id) {
                player.sendMessage("§cThis key doesn't match the chest tier!")
                return
            }
            
            if (tier.lockMode == LockMode.INSTANCE) {
                if (keyData.boundChestUUID != null && keyData.boundChestUUID != chest.uuid) {
                    player.sendMessage("§cThis key is bound to a different chest!")
                    return
                }
            }
            
            // Consume key
            val shouldConsume = when (tier.keyConsumeMode) {
                KeyConsumeMode.NEVER -> false
                KeyConsumeMode.ON_OPEN -> true
                KeyConsumeMode.ON_FIRST_OPEN -> !chest.firstOpenDone
            }
            
            if (shouldConsume) {
                val keepKey = plugin.keyManager.consumeUse(itemInHand)
                if (!keepKey) {
                    player.inventory.setItemInMainHand(null)
                    player.sendMessage("§eYour key was consumed!")
                }
            }
        }
        
        // Fire event
        val openEvent = ChestOpenEvent(player, chest, tier)
        plugin.server.pluginManager.callEvent(openEvent)
        
        if (openEvent.isCancelled) {
            return
        }
        
        // Generate and give loot
        val loot = plugin.lootManager.generateLoot(chest.lootTableId, chest.location)
        
        for (item in loot) {
            val leftover = player.inventory.addItem(item)
            if (leftover.isNotEmpty()) {
                leftover.values.forEach { 
                    chest.location.world.dropItemNaturally(chest.location, it)
                }
            }
        }
        
        // Play effects
        tier.openSound?.let { chest.location.world.playSound(chest.location, it, 1.0f, 1.0f) }
        tier.particles?.let { p ->
            chest.location.world.spawnParticle(
                p.particle,
                chest.location.clone().add(0.5, 0.5, 0.5),
                p.count,
                p.offsetX,
                p.offsetY,
                p.offsetZ,
                p.speed
            )
        }
        
        // Update chest state
        plugin.chestManager.openChest(chest)
        
        player.sendMessage("§aYou opened a ${tier.displayName}§a!")
        player.sendMessage("§7Received ${loot.size} item(s)")
    }
}

