package com.tieredchests.manager

import com.tieredchests.TieredChestsPlugin
import com.tieredchests.model.*
import org.bukkit.Material
import org.bukkit.Particle
import org.bukkit.Sound
import org.bukkit.configuration.ConfigurationSection
import java.util.logging.Level

class TierManager(private val plugin: TieredChestsPlugin) {
    
    private val tiers = mutableMapOf<String, TierDefinition>()
    
    init {
        loadTiers()
    }
    
    private fun loadTiers() {
        tiers.clear()
        
        val tiersSection = plugin.configManager.tiersConfig.getConfigurationSection("tiers")
            ?: run {
                plugin.logger.warning("No tiers defined in tiers.yml!")
                return
            }
        
        for (key in tiersSection.getKeys(false)) {
            val tierSection = tiersSection.getConfigurationSection(key) ?: continue
            
            try {
                val tier = parseTier(key, tierSection)
                tiers[tier.id] = tier
                plugin.logger.info("Loaded tier: ${tier.id}")
            } catch (e: Exception) {
                plugin.logger.log(Level.SEVERE, "Failed to load tier: $key", e)
            }
        }
    }
    
    private fun parseTier(id: String, section: ConfigurationSection): TierDefinition {
        val displayName = section.getString("displayName", id)!!
        val colorName = section.getString("colorName", "WHITE")!!
        val coreBlock = Material.valueOf(section.getString("coreBlock", "BARREL")!!.uppercase())
        val lockMode = LockMode.valueOf(section.getString("lockMode", "TIER")!!.uppercase())
        val keyConsumeMode = KeyConsumeMode.valueOf(section.getString("keyConsumeMode", "ON_OPEN")!!.uppercase())
        val accentScheme = section.getString("accentScheme", "CartPlate")!!
        
        // Parse accents
        val accents = mutableListOf<AccentPiece>()
        val accentsSection = section.getConfigurationSection("accents")
        if (accentsSection != null) {
            for (accentKey in accentsSection.getKeys(false)) {
                val accentList = accentsSection.getList(accentKey)
                if (accentList != null) {
                    for (item in accentList) {
                        if (item is Map<*, *>) {
                            accents.add(parseAccentPiece(item))
                        }
                    }
                }
            }
        }
        
        // Parse text badge
        val textBadge = section.getConfigurationSection("textBadge")?.let { badge ->
            if (badge.getBoolean("enabled", false)) {
                val content = badge.getString("content", "")!!
                val offsetSection = badge.getConfigurationSection("offset")
                val offset = if (offsetSection != null) {
                    Vector3D(
                        offsetSection.getDouble("x", 0.0),
                        offsetSection.getDouble("y", 1.05),
                        offsetSection.getDouble("z", -0.51)
                    )
                } else {
                    Vector3D(0.0, 1.05, -0.51)
                }
                val scale = badge.getDouble("scale", 0.5).toFloat()
                val billboard = Billboard.valueOf(badge.getString("billboard", "CENTER")!!.uppercase())
                TextBadge(true, content, offset, scale, billboard)
            } else null
        }
        
        // Parse sounds
        val openSound = section.getString("openSound")?.let { 
            try { Sound.valueOf(it.uppercase()) } catch (e: Exception) { null }
        }
        val closeSound = section.getString("closeSound")?.let {
            try { Sound.valueOf(it.uppercase()) } catch (e: Exception) { null }
        }
        
        // Parse particles
        val particles = section.getConfigurationSection("particles")?.let { p ->
            val particle = Particle.valueOf(p.getString("particle", "VILLAGER_HAPPY")!!.uppercase())
            ParticleEffect(
                particle,
                p.getInt("count", 5),
                p.getDouble("offsetX", 0.5),
                p.getDouble("offsetY", 0.5),
                p.getDouble("offsetZ", 0.5),
                p.getDouble("speed", 0.1)
            )
        }
        
        // Parse respawn policy
        val respawnPolicy = parseRespawnPolicy(section.getConfigurationSection("respawnPolicy"))
        
        return TierDefinition(
            id, displayName, colorName, coreBlock, lockMode, keyConsumeMode,
            accentScheme, accents, textBadge, openSound, closeSound, particles, respawnPolicy
        )
    }
    
    private fun parseAccentPiece(map: Map<*, *>): AccentPiece {
        val type = DisplayType.valueOf((map["type"] as String).uppercase())
        val material = (map["block"] as? String)?.let { Material.valueOf(it.uppercase()) }
        val item = (map["item"] as? String)?.let { Material.valueOf(it.uppercase()) }
        
        val transformMap = map["transform"] as? Map<*, *> ?: emptyMap<String, Any>()
        val transform = Transform(
            (transformMap["sx"] as? Number)?.toDouble() ?: 1.0,
            (transformMap["sy"] as? Number)?.toDouble() ?: 1.0,
            (transformMap["sz"] as? Number)?.toDouble() ?: 1.0,
            (transformMap["rx"] as? Number)?.toDouble() ?: 0.0,
            (transformMap["ry"] as? Number)?.toDouble() ?: 0.0,
            (transformMap["rz"] as? Number)?.toDouble() ?: 0.0,
            (transformMap["tx"] as? Number)?.toDouble() ?: 0.0,
            (transformMap["ty"] as? Number)?.toDouble() ?: 0.0,
            (transformMap["tz"] as? Number)?.toDouble() ?: 0.0
        )
        
        return AccentPiece(type, material, item, transform)
    }
    
    private fun parseRespawnPolicy(section: ConfigurationSection?): RespawnPolicy {
        if (section == null) return RespawnPolicy.None
        
        return when (section.getString("type", "NONE")!!.uppercase()) {
            "FIXED" -> RespawnPolicy.Fixed(section.getInt("minutes", 60))
            "CRON" -> RespawnPolicy.Cron(section.getString("expression", "0 0 * * *")!!)
            else -> RespawnPolicy.None
        }
    }
    
    fun getTier(id: String): TierDefinition? = tiers[id]
    
    fun getTiers(): Collection<TierDefinition> = tiers.values
    
    fun registerTier(tier: TierDefinition) {
        tiers[tier.id] = tier
    }
}

