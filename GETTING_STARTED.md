# Getting Started with Custom Tiered Chests

## 🎉 Build Successful!

Your Custom Tiered Chests plugin has been successfully built!

**Output JAR:** `plugin/target/custom-tiered-chests-plugin-1.0.0.jar`

## 📦 Installation

1. **Copy the JAR** to your server's `plugins/` folder:
   ```
   plugin/target/custom-tiered-chests-plugin-1.0.0.jar
   → your-server/plugins/
   ```

2. **Start your server** (Paper/Purpur 1.20.4+)

3. **Check the console** for:
   ```
   [CustomTieredChests] Custom Tiered Chests v1.0.0 enabled!
   [CustomTieredChests] Loaded 4 tiers
   [CustomTieredChests] Loaded 0 chest instances
   ```

4. **Configuration files** will be auto-generated in `plugins/CustomTieredChests/`:
   - `config.yml` - General settings
   - `tiers.yml` - Tier definitions and visuals
   - `loot_tables.yml` - Custom loot tables
   - `chests/` - Per-instance data

## 🚀 Quick Test

### 1. Give yourself a chest
```bash
/ctc givechest <your-name> common 1
```

### 2. Place the chest
- Right-click to place
- A GUI will open to select a loot table
- Choose any loot table (e.g., `starter_common`)

### 3. Give yourself a key
```bash
/ctc givekey <your-name> common
```

### 4. Open the chest
- Right-click the placed chest with the key
- Loot will be added to your inventory!

## 🎨 Visual Features

Each tier has unique Display Entity decorations:

### Common Tier
- Minecart item as lid plate
- Chains at corners
- Gray text badge

### Rare Tier
- Rail bands around chest
- Blackstone button rivets
- Iron ingot accent
- Blue text badge

### Epic Tier
- Amethyst clusters at corners
- Trapdoor veneers
- Gold ingot accent
- Purple text badge

### Mythic Tier
- Obsidian core inset
- Netherite scrap studs
- Tall chains at corners
- Pink text badge

## 🔧 Customization

### Creating Custom Tiers

Edit `plugins/CustomTieredChests/tiers.yml`:

```yaml
tiers:
  my_custom_tier:
    displayName: "&6My Custom Chest"
    colorName: GOLD
    coreBlock: BARREL
    lockMode: TIER
    keyConsumeMode: ON_OPEN
    accentScheme: Custom
    accents:
      decorations:
        - type: ITEM_DISPLAY
          item: DIAMOND
          transform:
            sx: 0.5
            sy: 0.5
            sz: 0.5
            tx: 0.0
            ty: 0.8
            tz: 0.0
    textBadge:
      enabled: true
      content: "&6CUSTOM"
      offset:
        x: 0.0
        y: 1.05
        z: -0.51
    openSound: BLOCK_CHEST_OPEN
    respawnPolicy:
      type: FIXED
      minutes: 30
```

Then reload:
```bash
/ctc reload
```

### Creating Custom Loot

Edit `plugins/CustomTieredChests/loot_tables.yml`:

```yaml
tables:
  my_loot:
    rolls: 3
    pools:
      - weight: 50
        item: "minecraft:diamond"
        min: 1
        max: 5
      - weight: 30
        item: "minecraft:emerald"
        min: 2
        max: 8
      - weight: 20
        item: "minecraft:netherite_ingot"
        min: 1
        max: 2
```

## 📚 Documentation

- **[README.md](README.md)** - Overview and features
- **[docs/CONFIG.md](docs/CONFIG.md)** - Configuration guide
- **[docs/COMMANDS.md](docs/COMMANDS.md)** - Commands and permissions
- **[docs/LOOT_TABLES.md](docs/LOOT_TABLES.md)** - Loot table guide
- **[docs/PERMISSIONS.md](docs/PERMISSIONS.md)** - Permission reference

## 🎯 Common Use Cases

### Event Rewards
```bash
# Give winner a mythic chest with unlimited key
/ctc givechest WinnerName mythic 1
/ctc givekey WinnerName mythic
```

### Dungeon Loot
```bash
# Place chests in dungeons with specific loot tables
# Use instance keys for one-time access
/ctc givekey PlayerName mythic 1 instance
```

### Shop Items
```bash
# Sell chest items in your shop
/ctc givechest @s rare 64
```

### Timed Events
```yaml
# In tiers.yml, set respawn policy
respawnPolicy:
  type: FIXED
  minutes: 1440  # Daily respawn
```

## 🔍 Troubleshooting

### Visuals not appearing
```bash
/ctc rebuild
```

### Loot not generating
- Check loot table ID is correct
- Use `/ctc inspect` to view chest info
- Try a vanilla loot table first

### Permission issues
```bash
# Give yourself admin permission
/lp user <name> permission set ctc.admin true
```

### Performance
- Reduce `maxDisplayEntities` in config.yml
- Disable particles if needed
- Check for orphaned entities

## 🌟 Advanced Features

### Folia Support
The plugin is fully Folia-compatible with region-thread safety.

### PlaceholderAPI
```
%ctc_tier% - Nearest chest tier
%ctc_ready_in% - Cooldown remaining
```

### WorldGuard Integration
Automatically checks region permissions for placement and opening.

### API Usage
```java
TieredChestsAPI api = TieredChestsPlugin.getInstance().getApi();
ChestInstance chest = api.getChest(location);
api.giveKey(player, "mythic", 3, KeyBindMode.TIER);
```

## 📞 Support

- **Issues**: Report bugs on GitHub
- **Wiki**: Check the wiki for guides
- **Discord**: Join the community

## 🎊 You're Ready!

Your Custom Tiered Chests plugin is ready to use! Start creating unique chest experiences for your players.

**Happy crafting! 🎮**

