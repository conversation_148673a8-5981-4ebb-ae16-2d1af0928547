package com.tieredchests.integration

import com.tieredchests.TieredChestsPlugin
import org.bukkit.Bukkit

class IntegrationManager(private val plugin: TieredChestsPlugin) {
    
    var hasPlaceholderAPI = false
        private set
    
    var hasWorldGuard = false
        private set
    
    var hasVault = false
        private set
    
    init {
        checkIntegrations()
    }
    
    private fun checkIntegrations() {
        // Check PlaceholderAPI
        if (Bukkit.getPluginManager().getPlugin("PlaceholderAPI") != null) {
            hasPlaceholderAPI = true
            plugin.logger.info("PlaceholderAPI found - integration enabled")
            registerPlaceholders()
        }
        
        // Check WorldGuard
        if (Bukkit.getPluginManager().getPlugin("WorldGuard") != null) {
            hasWorldGuard = true
            plugin.logger.info("WorldGuard found - integration enabled")
        }
        
        // Check Vault
        if (Bukkit.getPluginManager().getPlugin("Vault") != null) {
            hasVault = true
            plugin.logger.info("Vault found - integration enabled")
        }
    }
    
    private fun registerPlaceholders() {
        try {
            PlaceholderAPIIntegration(plugin).register()
        } catch (e: Exception) {
            plugin.logger.warning("Failed to register PlaceholderAPI integration: ${e.message}")
        }
    }
}

