# Example tier configuration with additional creative designs

configVersion: 1

tiers:
  # Starter tier - Simple wooden aesthetic
  wooden:
    displayName: "&eWooden Chest"
    colorName: YELLOW
    coreBlock: BARREL
    lockMode: NONE
    keyConsumeMode: NEVER
    accentScheme: Simple
    accents:
      simple:
        - type: BLOCK_DISPLAY
          block: OAK_TRAPDOOR
          transform:
            sx: 1.05
            sy: 0.1
            sz: 1.05
            rx: 0
            ry: 0
            rz: 0
            tx: 0.0
            ty: 0.0
            tz: 0.0
    textBadge:
      enabled: false
    openSound: BLOCK_WOOD_PLACE
    respawnPolicy:
      type: FIXED
      minutes: 30

  # Ocean themed chest
  ocean:
    displayName: "&bOcean Chest"
    colorName: AQUA
    coreBlock: BARREL
    lockMode: TIER
    keyConsumeMode: ON_OPEN
    accentScheme: Ocean
    accents:
      coral:
        - type: BLOCK_DISPLAY
          block: TUBE_CORAL
          transform:
            sx: 0.3
            sy: 0.3
            sz: 0.3
            rx: 0
            ry: 0
            rz: 0
            tx: -0.4
            ty: 0.6
            tz: -0.4
        - type: BLOCK_DISPLAY
          block: BRAIN_CORAL
          transform:
            sx: 0.3
            sy: 0.3
            sz: 0.3
            rx: 0
            ry: 0
            rz: 0
            tx: 0.4
            ty: 0.6
            tz: 0.4
      prismarine:
        - type: BLOCK_DISPLAY
          block: PRISMARINE
          transform:
            sx: 0.9
            sy: 0.9
            sz: 0.9
            rx: 0
            ry: 45
            rz: 0
            tx: 0.0
            ty: 0.05
            tz: 0.0
    textBadge:
      enabled: true
      content: "&b≈ OCEAN ≈"
      offset:
        x: 0.0
        y: 1.05
        z: -0.51
      scale: 0.5
    openSound: ENTITY_GUARDIAN_AMBIENT
    particles:
      particle: DRIP_WATER
      count: 10
      offsetX: 0.5
      offsetY: 0.5
      offsetZ: 0.5
      speed: 0.1
    respawnPolicy:
      type: FIXED
      minutes: 90

  # Nether themed chest
  nether:
    displayName: "&cNether Chest"
    colorName: RED
    coreBlock: BARREL
    lockMode: TIER
    keyConsumeMode: ON_FIRST_OPEN
    accentScheme: Nether
    accents:
      lava:
        - type: BLOCK_DISPLAY
          block: MAGMA_BLOCK
          transform:
            sx: 0.95
            sy: 0.95
            sz: 0.95
            rx: 0
            ry: 0
            rz: 0
            tx: 0.0
            ty: 0.0
            tz: 0.0
      chains:
        - type: BLOCK_DISPLAY
          block: CHAIN
          transform:
            sx: 0.2
            sy: 1.0
            sz: 0.2
            rx: 0
            ry: 0
            rz: 0
            tx: -0.5
            ty: 0.0
            tz: 0.0
        - type: BLOCK_DISPLAY
          block: CHAIN
          transform:
            sx: 0.2
            sy: 1.0
            sz: 0.2
            rx: 0
            ry: 0
            rz: 0
            tx: 0.5
            ty: 0.0
            tz: 0.0
      blazeRods:
        - type: ITEM_DISPLAY
          item: BLAZE_ROD
          transform:
            sx: 0.4
            sy: 0.4
            sz: 0.4
            rx: 45
            ry: 0
            rz: 0
            tx: 0.0
            ty: 0.7
            tz: 0.0
    textBadge:
      enabled: true
      content: "&c⚠ NETHER ⚠"
      offset:
        x: 0.0
        y: 1.1
        z: -0.51
      scale: 0.6
    openSound: BLOCK_LAVA_POP
    particles:
      particle: FLAME
      count: 15
      offsetX: 0.5
      offsetY: 0.5
      offsetZ: 0.5
      speed: 0.05
    respawnPolicy:
      type: FIXED
      minutes: 180

  # End themed chest
  end:
    displayName: "&5End Chest"
    colorName: DARK_PURPLE
    coreBlock: ENDER_CHEST
    lockMode: INSTANCE
    keyConsumeMode: ON_FIRST_OPEN
    accentScheme: End
    accents:
      endRods:
        - type: BLOCK_DISPLAY
          block: END_ROD
          transform:
            sx: 0.15
            sy: 0.8
            sz: 0.15
            rx: 0
            ry: 0
            rz: 0
            tx: -0.45
            ty: 0.2
            tz: -0.45
        - type: BLOCK_DISPLAY
          block: END_ROD
          transform:
            sx: 0.15
            sy: 0.8
            sz: 0.15
            rx: 0
            ry: 0
            rz: 0
            tx: 0.45
            ty: 0.2
            tz: -0.45
        - type: BLOCK_DISPLAY
          block: END_ROD
          transform:
            sx: 0.15
            sy: 0.8
            sz: 0.15
            rx: 0
            ry: 0
            rz: 0
            tx: -0.45
            ty: 0.2
            tz: 0.45
        - type: BLOCK_DISPLAY
          block: END_ROD
          transform:
            sx: 0.15
            sy: 0.8
            sz: 0.15
            rx: 0
            ry: 0
            rz: 0
            tx: 0.45
            ty: 0.2
            tz: 0.45
      purpur:
        - type: BLOCK_DISPLAY
          block: PURPUR_BLOCK
          transform:
            sx: 0.85
            sy: 0.85
            sz: 0.85
            rx: 0
            ry: 45
            rz: 0
            tx: 0.0
            ty: 0.1
            tz: 0.0
      pearl:
        - type: ITEM_DISPLAY
          item: ENDER_PEARL
          transform:
            sx: 0.5
            sy: 0.5
            sz: 0.5
            rx: 0
            ry: 0
            rz: 0
            tx: 0.0
            ty: 0.8
            tz: 0.0
    textBadge:
      enabled: true
      content: "&5◆ END ◆"
      offset:
        x: 0.0
        y: 1.15
        z: -0.51
      scale: 0.65
    openSound: ENTITY_ENDERMAN_TELEPORT
    particles:
      particle: PORTAL
      count: 25
      offsetX: 0.5
      offsetY: 0.5
      offsetZ: 0.5
      speed: 0.1
    respawnPolicy:
      type: FIXED
      minutes: 360

