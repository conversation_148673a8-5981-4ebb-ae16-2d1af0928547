package com.tieredchests.util

import org.bukkit.Bukkit
import org.bukkit.Location
import org.bukkit.entity.Entity
import org.bukkit.plugin.Plugin

object FoliaUtil {
    
    private val isFolia: Boolean by lazy {
        try {
            Class.forName("io.papermc.paper.threadedregions.RegionizedServer")
            true
        } catch (e: ClassNotFoundException) {
            false
        }
    }
    
    fun runAtLocation(plugin: Plugin, location: Location, task: Runnable) {
        if (isFolia) {
            // Use Folia's region scheduler
            try {
                val scheduler = Bukkit.getServer()::class.java.getMethod("getRegionScheduler").invoke(Bukkit.getServer())
                scheduler::class.java.getMethod("run", Plugin::class.java, Location::class.java, java.util.function.Consumer::class.java)
                    .invoke(scheduler, plugin, location, java.util.function.Consumer<Any> { task.run() })
            } catch (e: Exception) {
                // Fallback
                task.run()
            }
        } else {
            // Standard Bukkit scheduler
            if (Bukkit.isPrimaryThread()) {
                task.run()
            } else {
                Bukkit.getScheduler().runTask(plugin, task)
            }
        }
    }
    
    fun runAtEntity(plugin: Plugin, entity: Entity, task: Runnable) {
        if (isFolia) {
            try {
                val scheduler = Bukkit.getServer()::class.java.getMethod("getRegionScheduler").invoke(Bukkit.getServer())
                scheduler::class.java.getMethod("run", Plugin::class.java, Location::class.java, java.util.function.Consumer::class.java)
                    .invoke(scheduler, plugin, entity.location, java.util.function.Consumer<Any> { task.run() })
            } catch (e: Exception) {
                task.run()
            }
        } else {
            if (Bukkit.isPrimaryThread()) {
                task.run()
            } else {
                Bukkit.getScheduler().runTask(plugin, task)
            }
        }
    }
    
    fun runAsync(plugin: Plugin, task: Runnable) {
        if (isFolia) {
            try {
                val scheduler = Bukkit.getServer()::class.java.getMethod("getAsyncScheduler").invoke(Bukkit.getServer())
                scheduler::class.java.getMethod("runNow", Plugin::class.java, java.util.function.Consumer::class.java)
                    .invoke(scheduler, plugin, java.util.function.Consumer<Any> { task.run() })
            } catch (e: Exception) {
                Bukkit.getScheduler().runTaskAsynchronously(plugin, task)
            }
        } else {
            Bukkit.getScheduler().runTaskAsynchronously(plugin, task)
        }
    }
}

