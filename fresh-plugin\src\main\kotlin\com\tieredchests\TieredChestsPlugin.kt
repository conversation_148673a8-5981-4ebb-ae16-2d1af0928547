package com.tieredchests

import org.bukkit.plugin.java.JavaPlugin

class TieredChestsPlugin : JavaPlugin() {
    
    companion object {
        lateinit var instance: TieredChestsPlugin
            private set
    }
    
    override fun onEnable() {
        instance = this
        
        logger.info("=== TIERED CHESTS FRESH - STARTING ===")
        
        // Register command
        val command = getCommand("ctc")
        if (command != null) {
            val executor = CommandHandler(this)
            command.setExecutor(executor)
            command.tabCompleter = executor
            logger.info("✓ Command registered successfully!")
        } else {
            logger.severe("✗ Failed to register command!")
            return
        }
        
        // Register listeners
        server.pluginManager.registerEvents(ChestListener(this), this)
        logger.info("✓ Listeners registered!")
        
        logger.info("=== TIERED CHESTS FRESH - ENABLED ===")
    }
    
    override fun onDisable() {
        logger.info("=== TIERED CHESTS FRESH - DISABLED ===")
    }
}
