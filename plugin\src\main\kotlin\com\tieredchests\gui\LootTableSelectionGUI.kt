package com.tieredchests.gui

import com.tieredchests.TieredChestsPlugin
import net.kyori.adventure.text.Component
import net.kyori.adventure.text.format.NamedTextColor
import org.bukkit.Bukkit
import org.bukkit.Location
import org.bukkit.Material
import org.bukkit.entity.Player
import org.bukkit.event.EventHandler
import org.bukkit.event.Listener
import org.bukkit.event.inventory.InventoryClickEvent
import org.bukkit.event.inventory.InventoryCloseEvent
import org.bukkit.inventory.Inventory
import org.bukkit.inventory.ItemStack

class LootTableSelectionGUI(
    private val plugin: TieredChestsPlugin,
    private val player: Player,
    private val location: Location,
    private val tierId: String
) : Listener {
    
    private val inventory: Inventory
    private var selected = false
    
    init {
        inventory = Bukkit.createInventory(null, 27, Component.text("Select Loot Table"))
        setupInventory()
        plugin.server.pluginManager.registerEvents(this, plugin)
    }
    
    private fun setupInventory() {
        val lootTables = plugin.lootManager.getLootTableIds()
        
        // Add custom loot tables
        var slot = 0
        for (tableId in lootTables) {
            if (slot >= 27) break
            
            val item = ItemStack(Material.CHEST)
            val meta = item.itemMeta
            meta.displayName(Component.text(tableId).color(NamedTextColor.GOLD))
            meta.lore(listOf(
                Component.text("Click to select").color(NamedTextColor.GRAY)
            ))
            item.itemMeta = meta
            
            inventory.setItem(slot++, item)
        }
        
        // Add some vanilla loot tables
        val vanillaTables = listOf(
            "minecraft:chests/simple_dungeon",
            "minecraft:chests/end_city_treasure",
            "minecraft:chests/nether_bridge",
            "minecraft:chests/buried_treasure",
            "minecraft:chests/woodland_mansion"
        )
        
        for (tableId in vanillaTables) {
            if (slot >= 27) break
            
            val item = ItemStack(Material.ENDER_CHEST)
            val meta = item.itemMeta
            meta.displayName(Component.text(tableId).color(NamedTextColor.AQUA))
            meta.lore(listOf(
                Component.text("Vanilla loot table").color(NamedTextColor.GRAY),
                Component.text("Click to select").color(NamedTextColor.GRAY)
            ))
            item.itemMeta = meta
            
            inventory.setItem(slot++, item)
        }
    }
    
    fun open() {
        player.openInventory(inventory)
    }
    
    @EventHandler
    fun onClick(event: InventoryClickEvent) {
        if (event.inventory != inventory) return
        if (event.whoClicked != player) return
        
        event.isCancelled = true
        
        val item = event.currentItem ?: return
        if (!item.hasItemMeta()) return
        
        val lootTableId = (item.itemMeta.displayName() as? Component)?.let {
            // Extract plain text from component
            val text = it.toString()
            text.substringAfter("text=").substringBefore(",").trim('"')
        } ?: return
        
        // Create the chest instance
        plugin.chestManager.createChest(location, tierId, lootTableId, player.uniqueId)
        
        player.sendMessage("§aChest created with loot table: $lootTableId")
        selected = true
        player.closeInventory()
    }
    
    @EventHandler
    fun onClose(event: InventoryCloseEvent) {
        if (event.inventory != inventory) return
        if (event.player != player) return
        
        InventoryClickEvent.getHandlerList().unregister(this)
        InventoryCloseEvent.getHandlerList().unregister(this)
        
        if (!selected) {
            // Default to first available loot table
            val defaultTable = plugin.lootManager.getLootTableIds().firstOrNull() ?: "minecraft:chests/simple_dungeon"
            plugin.chestManager.createChest(location, tierId, defaultTable, player.uniqueId)
            player.sendMessage("§eChest created with default loot table: $defaultTable")
        }
    }
}

