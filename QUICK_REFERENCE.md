# Quick Reference Card

## Installation

1. Copy `plugin/target/custom-tiered-chests-plugin-1.0.0.jar` to `plugins/`
2. Start server
3. Configure `plugins/CustomTieredChests/` files
4. Reload with `/ctc reload`

## Essential Commands

| Command | Description | Permission |
|---------|-------------|------------|
| `/ctc` | Show help | None |
| `/ctc givechest <player> <tier> <amount>` | Give chest item | `ctc.admin.givechest` |
| `/ctc givekey <player> <tier> [uses] [instance]` | Give key | `ctc.admin.givekey` |
| `/ctc reload` | Reload configs | `ctc.admin.reload` |
| `/ctc rebuild` | Rebuild all shells | `ctc.admin.rebuild` |
| `/ctc inspect` | Inspect chest | `ctc.admin.inspect` |

## Default Tiers

| Tier | Color | Core Block | Visuals |
|------|-------|------------|---------|
| Common | Gray | CHEST | Minecart lid, chains |
| Rare | Blue | CHEST | Rails, blackstone, iron |
| Epic | Purple | BARREL | Amethyst, trapdoors, gold |
| Mythic | Pink | BARREL | Obsidian, netherite, tall chains |

## Default Loot Tables

- `starter_common` - Basic items
- `rare_tools` - Enchanted tools
- `epic_treasure` - Valuable items
- `mythic_end` - End-game loot
- `ocean_bounty` - Ocean items
- `nether_cache` - Nether items

## Permissions

### Admin Permissions
- `ctc.admin` - Full admin access (includes all below)
- `ctc.admin.givechest` - Give chests
- `ctc.admin.givekey` - Give keys
- `ctc.admin.setloot` - Set loot tables
- `ctc.admin.inspect` - Inspect chests
- `ctc.admin.reload` - Reload plugin
- `ctc.admin.rebuild` - Rebuild shells

### Player Permissions
- `ctc.use` - Use chests (default: true)
- `ctc.place` - Place chests (default: true)
- `ctc.break` - Break chests (default: true)

## Quick Start

### 1. Give yourself a chest
```bash
/ctc givechest <your-name> common 1
```

### 2. Place the chest
- Right-click to place
- GUI opens automatically
- Select a loot table

### 3. Give yourself a key
```bash
/ctc givekey <your-name> common
```

### 4. Open the chest
- Right-click with key
- Loot appears in inventory

## Configuration Files

### config.yml
```yaml
debug: false
maxDisplayEntities: 10
saveInterval: 300
```

### tiers.yml
```yaml
tiers:
  tier_id:
    displayName: "&6Custom Tier"
    colorName: GOLD
    coreBlock: CHEST
    lockMode: TIER
    keyConsumeMode: ON_OPEN
    accentScheme: Custom
    accents:
      decorations:
        - type: ITEM_DISPLAY
          item: DIAMOND
          transform:
            sx: 0.5  # scale x
            sy: 0.5  # scale y
            sz: 0.5  # scale z
            tx: 0.0  # translate x
            ty: 0.8  # translate y
            tz: 0.0  # translate z
    textBadge:
      enabled: true
      content: "&6CUSTOM"
    respawnPolicy:
      type: FIXED
      minutes: 30
```

### loot_tables.yml
```yaml
tables:
  table_id:
    rolls: 3
    pools:
      - weight: 50
        item: "minecraft:diamond"
        min: 1
        max: 5
```

## Display Entity Types

- `BLOCK_DISPLAY` - Display a block
- `ITEM_DISPLAY` - Display an item
- `TEXT_DISPLAY` - Display text

## Transform Parameters

- `sx, sy, sz` - Scale (0.0 to 2.0)
- `rx, ry, rz` - Rotation in degrees (0 to 360)
- `tx, ty, tz` - Translation (-1.0 to 1.0)

## Lock Modes

- `TIER` - Any key of same tier works
- `INSTANCE` - Only specific instance key works
- `NONE` - No key required

## Key Consume Modes

- `ON_OPEN` - Key consumed when chest opens
- `ON_LOOT` - Key consumed when loot taken
- `NEVER` - Key never consumed

## Respawn Policies

### Fixed
```yaml
respawnPolicy:
  type: FIXED
  minutes: 30
```

### Cron
```yaml
respawnPolicy:
  type: CRON
  expression: "0 0 * * *"  # Daily at midnight
```

### Never
```yaml
respawnPolicy:
  type: NEVER
```

## Vanilla Materials for Accents

### Blocks
- CHAIN, IRON_BARS, LANTERN
- AMETHYST_CLUSTER, AMETHYST_BUD
- OBSIDIAN, CRYING_OBSIDIAN
- BLACKSTONE, POLISHED_BLACKSTONE
- TRAPDOOR variants
- BUTTON variants

### Items
- MINECART, RAIL variants
- IRON_INGOT, GOLD_INGOT, DIAMOND
- NETHERITE_SCRAP, NETHERITE_INGOT
- ENDER_PEARL, ENDER_EYE
- NETHER_STAR

## Troubleshooting

### Commands not working
- Check you're OP or have permissions
- Check console for errors
- Try `/plugins` to verify plugin is loaded

### GUI not opening
- Check `ctc.place` permission
- Check console logs
- Verify using tiered chest item (not regular chest)

### Loot not generating
- Check loot table exists
- Check chest not on cooldown
- Verify key tier matches chest tier

### Display entities missing
- Run `/ctc rebuild`
- Check chunk is loaded
- Verify entities not killed

## API Usage

```java
// Get API
TieredChestsAPI api = TieredChestsPlugin.getInstance().getApi();

// Get chest at location
ChestInstance chest = api.getChest(location);

// Give key to player
api.giveKey(player, "mythic", 3, KeyBindMode.TIER);

// Register custom tier
TierDefinition tier = new TierDefinition(...);
api.registerTier(tier);
```

## PlaceholderAPI Placeholders

- `%ctc_tier%` - Nearest chest tier
- `%ctc_ready_in%` - Cooldown remaining

## File Locations

- **JAR**: `plugin/target/custom-tiered-chests-plugin-1.0.0.jar`
- **Config**: `plugins/CustomTieredChests/config.yml`
- **Tiers**: `plugins/CustomTieredChests/tiers.yml`
- **Loot**: `plugins/CustomTieredChests/loot_tables.yml`
- **Data**: `plugins/CustomTieredChests/chests/<uuid>.yml`

## Support

- **Docs**: See `docs/` folder
- **Troubleshooting**: See `TROUBLESHOOTING.md`
- **Changelog**: See `CHANGELOG.md`
- **Examples**: See `examples/config/`

## Version Info

- **Version**: 1.0.0
- **Minecraft**: 1.20.4+
- **Server**: Paper/Purpur
- **Java**: 17+
- **Folia**: Supported (experimental)

## Build Info

- **Build Tool**: Maven 3.9.11
- **Language**: Kotlin 1.9.22
- **Build Command**: `mvn clean package`
- **Output**: `plugin/target/custom-tiered-chests-plugin-1.0.0.jar`

