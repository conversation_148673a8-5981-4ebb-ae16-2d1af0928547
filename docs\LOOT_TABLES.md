# Loot Tables Guide

## Overview

Custom Tiered Chests supports two types of loot tables:
1. **Custom loot tables** (defined in `loot_tables.yml`)
2. **Vanilla loot tables** (Minecraft's built-in tables)

## Custom Loot Tables

### Basic Structure

```yaml
tables:
  table_id:
    rolls: 3              # How many items to generate
    pools:
      - weight: 50        # Relative probability
        item: "minecraft:diamond"
        min: 1            # Minimum stack size
        max: 3            # Maximum stack size
```

### Example: Starter Kit

```yaml
starter_kit:
  rolls: 5
  pools:
    - weight: 100
      item: "minecraft:bread"
      min: 8
      max: 16
    - weight: 80
      item: "minecraft:cooked_beef"
      min: 4
      max: 8
    - weight: 60
      item: "minecraft:iron_sword"
      min: 1
      max: 1
    - weight: 40
      item: "minecraft:iron_pickaxe"
      min: 1
      max: 1
    - weight: 20
      item: "minecraft:diamond"
      min: 1
      max: 3
```

### How Weights Work

Weights are **relative probabilities**:

```yaml
pools:
  - weight: 70    # 70% chance
    item: "minecraft:iron_ingot"
  - weight: 25    # 25% chance
    item: "minecraft:gold_ingot"
  - weight: 5     # 5% chance
    item: "minecraft:diamond"
```

Total weight = 70 + 25 + 5 = 100

### Multiple Rolls

```yaml
lucky_chest:
  rolls: 10       # Generate 10 items
  pools:
    - weight: 50
      item: "minecraft:emerald"
      min: 1
      max: 5
    - weight: 50
      item: "minecraft:diamond"
      min: 1
      max: 3
```

This will give 10 items, each randomly selected from the pools.

## Vanilla Loot Tables

You can use any Minecraft loot table by prefixing with `minecraft:`:

### Chest Loot Tables

```yaml
# Dungeon loot
minecraft:chests/simple_dungeon
minecraft:chests/abandoned_mineshaft

# Structure loot
minecraft:chests/end_city_treasure
minecraft:chests/nether_bridge
minecraft:chests/stronghold_corridor
minecraft:chests/woodland_mansion

# Buried treasure
minecraft:chests/buried_treasure
minecraft:chests/shipwreck_treasure

# Village loot
minecraft:chests/village/village_armorer
minecraft:chests/village/village_weaponsmith
minecraft:chests/village/village_toolsmith
```

### Entity Loot Tables

```yaml
# Boss loot
minecraft:entities/ender_dragon
minecraft:entities/wither

# Mob loot
minecraft:entities/zombie
minecraft:entities/skeleton
minecraft:entities/creeper
```

### Using in GUI

When placing a chest, the GUI shows:
- All custom tables from `loot_tables.yml`
- Common vanilla tables

You can also set via command:
```bash
/ctc setloot minecraft:chests/end_city_treasure
```

## Preset Loot Tables

The plugin includes these preset tables:

### starter_common
Basic survival items (bread, iron, occasional diamond)

### rare_tools
Iron and diamond tools

### epic_treasure
Diamonds, emeralds, enchanted golden apples, netherite scraps

### mythic_end
Ender pearls, netherite, totems, elytra

### ocean_bounty
Prismarine, heart of the sea, trident

### nether_cache
Ancient debris, netherite, blaze rods

## Advanced Examples

### Tiered Rewards

```yaml
tier1_rewards:
  rolls: 2
  pools:
    - weight: 90
      item: "minecraft:iron_ingot"
      min: 5
      max: 10
    - weight: 10
      item: "minecraft:diamond"
      min: 1
      max: 2

tier2_rewards:
  rolls: 3
  pools:
    - weight: 70
      item: "minecraft:diamond"
      min: 3
      max: 8
    - weight: 30
      item: "minecraft:netherite_scrap"
      min: 1
      max: 3

tier3_rewards:
  rolls: 5
  pools:
    - weight: 50
      item: "minecraft:netherite_ingot"
      min: 2
      max: 5
    - weight: 30
      item: "minecraft:enchanted_golden_apple"
      min: 1
      max: 3
    - weight: 20
      item: "minecraft:elytra"
      min: 1
      max: 1
```

### Themed Loot

```yaml
mining_cache:
  rolls: 4
  pools:
    - weight: 40
      item: "minecraft:iron_ore"
      min: 10
      max: 32
    - weight: 30
      item: "minecraft:gold_ore"
      min: 5
      max: 16
    - weight: 20
      item: "minecraft:diamond_ore"
      min: 2
      max: 8
    - weight: 10
      item: "minecraft:ancient_debris"
      min: 1
      max: 3

farming_bounty:
  rolls: 5
  pools:
    - weight: 50
      item: "minecraft:wheat_seeds"
      min: 16
      max: 64
    - weight: 30
      item: "minecraft:carrot"
      min: 8
      max: 32
    - weight: 15
      item: "minecraft:golden_carrot"
      min: 4
      max: 16
    - weight: 5
      item: "minecraft:enchanted_golden_apple"
      min: 1
      max: 1

combat_gear:
  rolls: 3
  pools:
    - weight: 40
      item: "minecraft:iron_sword"
      min: 1
      max: 1
    - weight: 30
      item: "minecraft:diamond_sword"
      min: 1
      max: 1
    - weight: 20
      item: "minecraft:netherite_sword"
      min: 1
      max: 1
    - weight: 10
      item: "minecraft:totem_of_undying"
      min: 1
      max: 1
```

## Best Practices

### 1. Balance Rolls and Weights

```yaml
# Good: Balanced
balanced_chest:
  rolls: 3
  pools:
    - weight: 50
      item: "minecraft:iron_ingot"
      min: 5
      max: 10
    - weight: 30
      item: "minecraft:gold_ingot"
      min: 3
      max: 6
    - weight: 20
      item: "minecraft:diamond"
      min: 1
      max: 3

# Bad: Too many rolls with rare items
overpowered_chest:
  rolls: 20
  pools:
    - weight: 100
      item: "minecraft:netherite_ingot"
      min: 64
      max: 64
```

### 2. Use Appropriate Ranges

```yaml
# Good: Reasonable ranges
good_range:
  - weight: 50
    item: "minecraft:diamond"
    min: 1
    max: 5

# Bad: Too wide range
bad_range:
  - weight: 50
    item: "minecraft:diamond"
    min: 1
    max: 64
```

### 3. Match Tier to Loot

```yaml
# Common tier
common_loot:
  rolls: 2
  pools:
    - weight: 70
      item: "minecraft:bread"
      min: 2
      max: 6

# Mythic tier
mythic_loot:
  rolls: 5
  pools:
    - weight: 50
      item: "minecraft:netherite_ingot"
      min: 2
      max: 5
```

## Testing Loot Tables

1. Create a test chest:
```bash
/ctc givechest @s common 1
```

2. Place and select your loot table

3. Give yourself a key:
```bash
/ctc givekey @s common
```

4. Open multiple times to test distribution

5. Adjust weights as needed

## Troubleshooting

### No items generated
- Check that `rolls` > 0
- Verify item IDs are correct (use `minecraft:` prefix)
- Ensure weights are positive numbers

### Wrong items
- Double-check item IDs in pools
- Verify you're using the correct loot table

### Too many/few items
- Adjust `rolls` value
- Modify `min` and `max` ranges

## Item ID Reference

Common items:
```
minecraft:diamond
minecraft:emerald
minecraft:netherite_ingot
minecraft:netherite_scrap
minecraft:ancient_debris
minecraft:enchanted_golden_apple
minecraft:totem_of_undying
minecraft:elytra
minecraft:trident
minecraft:heart_of_the_sea
```

See [Minecraft Wiki](https://minecraft.wiki/w/Java_Edition_data_values) for full list.

