package com.tieredchests.manager

import com.tieredchests.TieredChestsPlugin
import com.tieredchests.model.ChestInstance
import com.tieredchests.model.RespawnPolicy
import com.tieredchests.util.FoliaUtil
import org.bukkit.Location
import org.bukkit.configuration.file.YamlConfiguration
import java.io.File
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import java.util.logging.Level

class ChestManager(private val plugin: TieredChestsPlugin) {
    
    private val chests = ConcurrentHashMap<UUID, ChestInstance>()
    private val locationIndex = ConcurrentHashMap<String, UUID>()
    
    fun createChest(location: Location, tierId: String, lootTableId: String, owner: UUID?): ChestInstance {
        val uuid = UUID.randomUUID()
        val chest = ChestInstance(
            uuid = uuid,
            location = location,
            tierId = tierId,
            lootTableId = lootTableId,
            owner = owner,
            placedAt = System.currentTimeMillis(),
            lastOpened = null,
            nextRespawn = null,
            firstOpenDone = false,
            shellEntities = emptyList(),
            badgeEnabled = true
        )
        
        chests[uuid] = chest
        locationIndex[locationKey(location)] = uuid
        
        // Spawn shell
        val tier = plugin.tierManager.getTier(tierId)
        if (tier != null) {
            val entities = plugin.shellManager.spawnShell(chest, tier)
            chest.shellEntities = entities
        }
        
        saveChest(chest)
        return chest
    }
    
    fun getChest(uuid: UUID): ChestInstance? = chests[uuid]
    
    fun getChestAt(location: Location): ChestInstance? {
        val uuid = locationIndex[locationKey(location)] ?: return null
        return chests[uuid]
    }
    
    fun removeChest(chest: ChestInstance) {
        plugin.shellManager.removeShell(chest)
        chests.remove(chest.uuid)
        locationIndex.remove(locationKey(chest.location))
        
        val file = File(plugin.configManager.getChestsDir(), "${chest.uuid}.yml")
        file.delete()
    }
    
    fun getAllChests(): Collection<ChestInstance> = chests.values
    
    fun openChest(chest: ChestInstance) {
        chest.lastOpened = System.currentTimeMillis()
        
        if (!chest.firstOpenDone) {
            chest.firstOpenDone = true
        }
        
        // Calculate next respawn
        val tier = plugin.tierManager.getTier(chest.tierId)
        if (tier != null) {
            chest.nextRespawn = calculateNextRespawn(tier.respawnPolicy)
        }
        
        saveChest(chest)
    }
    
    private fun calculateNextRespawn(policy: RespawnPolicy): Long? {
        return when (policy) {
            is RespawnPolicy.None -> null
            is RespawnPolicy.Fixed -> System.currentTimeMillis() + (policy.minutes * 60000L)
            is RespawnPolicy.Cron -> {
                // Simplified cron - would need proper cron parser in production
                System.currentTimeMillis() + (60 * 60000L) // Default 1 hour
            }
        }
    }
    
    fun processRespawns() {
        val now = System.currentTimeMillis()
        
        for (chest in chests.values) {
            if (chest.nextRespawn != null && now >= chest.nextRespawn!!) {
                chest.nextRespawn = null
                saveChest(chest)
            }
        }
    }
    
    fun rebuildAllShells() {
        for (chest in chests.values) {
            val tier = plugin.tierManager.getTier(chest.tierId) ?: continue
            plugin.shellManager.rebuildShell(chest, tier)
        }
    }
    
    fun rebuildShellAt(location: Location) {
        val chest = getChestAt(location) ?: return
        val tier = plugin.tierManager.getTier(chest.tierId) ?: return
        plugin.shellManager.rebuildShell(chest, tier)
    }
    
    fun saveChest(chest: ChestInstance) {
        FoliaUtil.runAsync(plugin, Runnable {
            try {
                val file = File(plugin.configManager.getChestsDir(), "${chest.uuid}.yml")
                val config = YamlConfiguration()
                
                config.set("uuid", chest.uuid.toString())
                config.set("world", chest.location.world.name)
                config.set("x", chest.location.x)
                config.set("y", chest.location.y)
                config.set("z", chest.location.z)
                config.set("tierId", chest.tierId)
                config.set("lootTableId", chest.lootTableId)
                config.set("owner", chest.owner?.toString())
                config.set("placedAt", chest.placedAt)
                config.set("lastOpened", chest.lastOpened)
                config.set("nextRespawn", chest.nextRespawn)
                config.set("firstOpenDone", chest.firstOpenDone)
                config.set("shellEntities", chest.shellEntities.map { it.toString() })
                config.set("badgeEnabled", chest.badgeEnabled)
                
                config.save(file)
            } catch (e: Exception) {
                plugin.logger.log(Level.SEVERE, "Failed to save chest ${chest.uuid}", e)
            }
        })
    }
    
    fun saveAll() {
        for (chest in chests.values) {
            saveChest(chest)
        }
    }
    
    fun loadAll() {
        val chestsDir = plugin.configManager.getChestsDir()
        val files = chestsDir.listFiles { file -> file.extension == "yml" } ?: return
        
        for (file in files) {
            try {
                val config = YamlConfiguration.loadConfiguration(file)
                
                val uuid = UUID.fromString(config.getString("uuid")!!)
                val world = plugin.server.getWorld(config.getString("world")!!) ?: continue
                val location = Location(
                    world,
                    config.getDouble("x"),
                    config.getDouble("y"),
                    config.getDouble("z")
                )
                
                val chest = ChestInstance(
                    uuid = uuid,
                    location = location,
                    tierId = config.getString("tierId")!!,
                    lootTableId = config.getString("lootTableId")!!,
                    owner = config.getString("owner")?.let { UUID.fromString(it) },
                    placedAt = config.getLong("placedAt"),
                    lastOpened = config.getLong("lastOpened").takeIf { it > 0 },
                    nextRespawn = config.getLong("nextRespawn").takeIf { it > 0 },
                    firstOpenDone = config.getBoolean("firstOpenDone"),
                    shellEntities = config.getStringList("shellEntities").map { UUID.fromString(it) },
                    badgeEnabled = config.getBoolean("badgeEnabled", true)
                )
                
                chests[uuid] = chest
                locationIndex[locationKey(location)] = uuid
                
            } catch (e: Exception) {
                plugin.logger.log(Level.SEVERE, "Failed to load chest from ${file.name}", e)
            }
        }
    }
    
    private fun locationKey(location: Location): String {
        return "${location.world.name}:${location.blockX}:${location.blockY}:${location.blockZ}"
    }
}

