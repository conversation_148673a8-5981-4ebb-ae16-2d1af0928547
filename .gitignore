# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# IntelliJ IDEA
.idea/
*.iml
*.iws
*.ipr
out/

# Eclipse
.classpath
.project
.settings/
bin/

# NetBeans
nbproject/
build/
nbbuild/
dist/
nbdist/
.nb-gradle/

# VS Code
.vscode/
*.code-workspace

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~
.directory

# Logs
*.log

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~

# Compiled class files
*.class

# Package files
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# Virtual machine crash logs
hs_err_pid*

# Kotlin
*.kotlin_module

# Gradle (if used)
.gradle/
gradle-app.setting
!gradle-wrapper.jar

# Test server
test-server/
server/
run/

