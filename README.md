# Custom Tiered Chests

[![Minecraft](https://img.shields.io/badge/Minecraft-1.20.4--1.21.x-green.svg)](https://papermc.io/)
[![Java](https://img.shields.io/badge/Java-17-orange.svg)](https://adoptium.net/)
[![Folia](https://img.shields.io/badge/Folia-Compatible-blue.svg)](https://papermc.io/software/folia)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

A production-ready Minecraft plugin that creates **visually distinct tiered chests** using Display Entities and vanilla assets only - **no resource pack required!**

## ✨ Key Features

### 🎨 No Resource Pack Required
- **100% server-side visuals** using Display Entities (1.20+)
- Built from vanilla blocks and items:
  - Minecarts, rails, chains, trapdoors
  - Amethyst clusters, obsidian, end rods
  - Gold ingots, netherite scraps, ender pearls
- Each tier has a unique visual silhouette
- Floating text badges (toggleable)

### 🔐 Flexible Key System
- **Tier keys**: Work on any chest of that tier
- **Instance keys**: Bound to specific chests
- Configurable consumption (never/on open/first open)
- Optional use limits

### 🎁 Advanced Loot System
- GUI-based loot table selection on placement
- Support for vanilla loot tables
- Custom weighted loot pools
- Per-chest persistence

### ⏰ Smart Respawn System
- Fixed cooldowns (e.g., 60 minutes)
- Cron expressions for scheduled respawns
- Visual cooldown indicators
- Per-tier policies

### 🌍 Folia-Safe Architecture
- Region-thread safe operations
- Async I/O for all file operations
- Zero blocking on server threads
- Paper/Purpur compatible

## 📦 Installation

1. **Download** the latest JAR from [Releases](https://github.com/yourusername/custom-tiered-chests/releases)
2. **Place** in your server's `plugins/` folder
3. **Restart** your server
4. **Configure** `tiers.yml` and `loot_tables.yml` (optional)

## 🚀 Quick Start

```bash
# Give yourself a chest
/ctc givechest <player> common 1

# Give yourself a key
/ctc givekey <player> common

# Place the chest and select a loot table
# Right-click with the key to open!
```

## 📊 Default Tiers

| Tier | Visual Theme | Lock | Respawn | Loot |
|------|-------------|------|---------|------|
| **Common** | Minecart plates + chains | Tier | 60m | Basic items |
| **Rare** | Rail bands + iron studs | Tier | 120m | Tools & materials |
| **Epic** | Amethyst gems + gold | Tier | 240m | Rare treasures |
| **Mythic** | Obsidian + netherite | Instance | 480m | Legendary items |

## 🛠️ Building from Source

### Requirements
- Java 17+
- Maven 3.6+

### Build Steps

**Windows:**
```batch
build.bat
```

**Linux/Mac:**
```bash
chmod +x build.sh
./build.sh
```

**Manual:**
```bash
C:\Users\<USER>\Downloads\apache-maven-3.9.11\bin\mvn clean package
```

Output: `plugin/target/custom-tiered-chests-plugin-1.0.0.jar`

## 📖 Documentation

- **[Configuration Guide](docs/CONFIG.md)** - Detailed config options
- **[Commands & Permissions](docs/COMMANDS.md)** - All commands and perms
- **[Loot Tables](docs/LOOT_TABLES.md)** - Creating custom loot
- **[Permissions](docs/PERMISSIONS.md)** - Permission reference

## 🎮 Usage Examples

### Creating Custom Tiers

Edit `tiers.yml`:

```yaml
tiers:
  custom:
    displayName: "&6Custom Chest"
    coreBlock: BARREL
    lockMode: TIER
    accents:
      decorations:
        - type: ITEM_DISPLAY
          item: GOLD_INGOT
          transform:
            sx: 0.5
            sy: 0.5
            sz: 0.5
            tx: 0.0
            ty: 0.7
            tz: 0.0
```

### Custom Loot Tables

Edit `loot_tables.yml`:

```yaml
tables:
  my_loot:
    rolls: 3
    pools:
      - weight: 50
        item: "minecraft:diamond"
        min: 1
        max: 5
      - weight: 50
        item: "minecraft:emerald"
        min: 2
        max: 8
```

### Using the API

```java
// Get the API
TieredChestsAPI api = TieredChestsPlugin.getInstance().getApi();

// Get a chest
ChestInstance chest = api.getChest(location);

// Give a key
api.giveKey(player, "mythic", 3, KeyBindMode.TIER);

// Register custom tier
TierDefinition tier = new TierDefinition(...);
api.registerTier(tier);
```

## 🔌 Integrations

- **PlaceholderAPI**: `%ctc_tier%`, `%ctc_ready_in%`
- **WorldGuard**: Region protection checks
- **Vault**: Economy integration (optional)

## 🎯 Design Philosophy

### Visual Design
- **No client mods**: Everything is server-side
- **Vanilla assets only**: No custom textures
- **Unique silhouettes**: Each tier is visually distinct
- **Performance-conscious**: Configurable entity limits

### Technical Design
- **Folia-first**: Built for region-threaded servers
- **Async I/O**: No blocking operations
- **Crash-safe**: Atomic writes, auto-recovery
- **Extensible**: Clean API for developers

## 📝 Commands

| Command | Description | Permission |
|---------|-------------|------------|
| `/ctc givechest <player> <tier> [amount]` | Give chest items | `ctc.admin.givechest` |
| `/ctc givekey <player> <tier> [uses] [bind]` | Give keys | `ctc.admin.givekey` |
| `/ctc setloot <lootTableId>` | Change loot table | `ctc.admin.setloot` |
| `/ctc inspect` | View chest info | `ctc.admin.inspect` |
| `/ctc togglebadge` | Toggle text badge | `ctc.admin.badge` |
| `/ctc rebuild` | Rebuild visual shell | `ctc.admin.rebuild` |
| `/ctc reload` | Reload configs | `ctc.admin.reload` |

## 🐛 Troubleshooting

### Visuals not appearing
```bash
/ctc rebuild
```

### Loot not generating
- Check loot table ID is correct
- Verify item IDs use `minecraft:` prefix
- Test with vanilla loot table first

### Performance issues
- Reduce `maxDisplayEntities` in config.yml
- Disable particles/sounds if needed
- Check for orphaned entities

## 🤝 Contributing

Contributions are welcome! Please:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

MIT License - See [LICENSE](LICENSE) file for details

## 🙏 Credits

- **Paper API** - Server platform
- **Display Entities** - Visual system (1.20+)
- **Kotlin** - Primary language
- **Maven** - Build system

## 📞 Support

- **Issues**: [GitHub Issues](https://github.com/yourusername/custom-tiered-chests/issues)
- **Wiki**: [GitHub Wiki](https://github.com/yourusername/custom-tiered-chests/wiki)
- **Discord**: [Join our Discord](#)

## 🗺️ Roadmap

- [ ] More preset tier designs
- [ ] Hologram cooldown displays
- [ ] Crafting recipes for keys/chests
- [ ] MySQL/PostgreSQL support
- [ ] Web dashboard
- [ ] Per-tier ambient effects

---

**Made with ❤️ for the Minecraft community**

