package com.tieredchests.manager

import com.tieredchests.TieredChestsPlugin
import com.tieredchests.model.*
import com.tieredchests.util.FoliaUtil
import net.kyori.adventure.text.Component
import net.kyori.adventure.text.minimessage.MiniMessage
import org.bukkit.Location
import org.bukkit.entity.BlockDisplay
import org.bukkit.entity.Display
import org.bukkit.entity.ItemDisplay
import org.bukkit.entity.TextDisplay
import org.bukkit.inventory.ItemStack
import org.bukkit.util.Transformation
import org.joml.AxisAngle4f
import org.joml.Vector3f
import java.util.*
import java.util.concurrent.ConcurrentHashMap

class ShellManager(private val plugin: TieredChestsPlugin) {
    
    private val shellCache = ConcurrentHashMap<UUID, List<UUID>>()
    private val miniMessage = MiniMessage.miniMessage()
    
    fun spawnShell(chest: ChestInstance, tier: TierDefinition): List<UUID> {
        val entities = mutableListOf<UUID>()
        val location = chest.location.clone().add(0.5, 0.0, 0.5)
        
        FoliaUtil.runAtLocation(plugin, location) {
            // Spawn accent pieces
            for (accent in tier.accents) {
                val entity = when (accent.type) {
                    DisplayType.BLOCK_DISPLAY -> spawnBlockDisplay(location, accent)
                    DisplayType.ITEM_DISPLAY -> spawnItemDisplay(location, accent)
                    DisplayType.TEXT_DISPLAY -> null // Handled separately
                }
                entity?.let { entities.add(it.uniqueId) }
            }
            
            // Spawn text badge if enabled
            if (chest.badgeEnabled && tier.textBadge != null && tier.textBadge.enabled) {
                val badge = spawnTextBadge(location, tier.textBadge)
                badge?.let { entities.add(it.uniqueId) }
            }
        }
        
        shellCache[chest.uuid] = entities
        return entities
    }
    
    private fun spawnBlockDisplay(location: Location, accent: AccentPiece): BlockDisplay? {
        val material = accent.material ?: return null
        
        return location.world.spawn(location, BlockDisplay::class.java) { display ->
            display.block = material.createBlockData()
            display.transformation = createTransformation(accent.transform)
            display.brightness = Display.Brightness(15, 15)
            display.isInvulnerable = true
            display.isPersistent = true
            
            // Make it non-interactive
            display.setGravity(false)
            display.isVisibleByDefault = true
        }
    }
    
    private fun spawnItemDisplay(location: Location, accent: AccentPiece): ItemDisplay? {
        val item = accent.item ?: return null
        
        return location.world.spawn(location, ItemDisplay::class.java) { display ->
            display.setItemStack(ItemStack(item))
            display.transformation = createTransformation(accent.transform)
            display.brightness = Display.Brightness(15, 15)
            display.isInvulnerable = true
            display.isPersistent = true
            display.itemDisplayTransform = ItemDisplay.ItemDisplayTransform.FIXED
            
            display.setGravity(false)
            display.isVisibleByDefault = true
        }
    }
    
    private fun spawnTextBadge(location: Location, badge: TextBadge): TextDisplay? {
        val badgeLocation = location.clone().add(badge.offset.x, badge.offset.y, badge.offset.z)
        
        return badgeLocation.world.spawn(badgeLocation, TextDisplay::class.java) { display ->
            display.text(miniMessage.deserialize(badge.content))
            display.billboard = when (badge.billboard) {
                Billboard.FIXED -> Display.Billboard.FIXED
                Billboard.VERTICAL -> Display.Billboard.VERTICAL
                Billboard.HORIZONTAL -> Display.Billboard.HORIZONTAL
                Billboard.CENTER -> Display.Billboard.CENTER
            }
            
            // Scale the text
            val scale = badge.scale
            val transformation = Transformation(
                Vector3f(0f, 0f, 0f),
                AxisAngle4f(0f, 0f, 0f, 1f),
                Vector3f(scale, scale, scale),
                AxisAngle4f(0f, 0f, 0f, 1f)
            )
            display.transformation = transformation
            
            display.brightness = Display.Brightness(15, 15)
            display.isInvulnerable = true
            display.isPersistent = true
            display.isSeeThrough = false
            display.backgroundColor = org.bukkit.Color.fromARGB(0, 0, 0, 0)
            
            display.setGravity(false)
            display.isVisibleByDefault = true
        }
    }
    
    private fun createTransformation(t: Transform): Transformation {
        // Translation
        val translation = Vector3f(t.tx.toFloat(), t.ty.toFloat(), t.tz.toFloat())
        
        // Rotation (convert degrees to radians and create quaternion)
        val rx = Math.toRadians(t.rx).toFloat()
        val ry = Math.toRadians(t.ry).toFloat()
        val rz = Math.toRadians(t.rz).toFloat()
        
        // Create rotation quaternion (simplified - proper quaternion math would be better)
        val leftRotation = AxisAngle4f(
            Math.sqrt((rx * rx + ry * ry + rz * rz).toDouble()).toFloat(),
            rx, ry, rz
        )
        
        // Scale
        val scale = Vector3f(t.sx.toFloat(), t.sy.toFloat(), t.sz.toFloat())
        
        // Right rotation (identity for now)
        val rightRotation = AxisAngle4f(0f, 0f, 0f, 1f)
        
        return Transformation(translation, leftRotation, scale, rightRotation)
    }
    
    fun removeShell(chest: ChestInstance) {
        val entities = shellCache.remove(chest.uuid) ?: chest.shellEntities
        
        FoliaUtil.runAtLocation(plugin, chest.location) {
            for (entityId in entities) {
                val entity = chest.location.world.getEntity(entityId)
                entity?.remove()
            }
        }
    }
    
    fun rebuildShell(chest: ChestInstance, tier: TierDefinition) {
        removeShell(chest)
        val newEntities = spawnShell(chest, tier)
        chest.shellEntities = newEntities
    }
    
    fun cleanupOrphans() {
        // Async task to find and remove orphaned display entities
        plugin.logger.info("Running orphan shell cleanup...")
        
        // This would need more sophisticated logic in production
        // For now, we rely on the persistent flag and manual cleanup
    }
    
    fun cleanupAll() {
        shellCache.clear()
    }
    
    fun updateCooldownDisplay(chest: ChestInstance, tier: TierDefinition) {
        if (!chest.isReady()) {
            val remaining = chest.getRemainingCooldown()
            val minutes = remaining / 60000
            val seconds = (remaining % 60000) / 1000
            
            // Find or spawn cooldown text display
            val location = chest.location.clone().add(0.5, 1.2, 0.5)
            
            FoliaUtil.runAtLocation(plugin, location) {
                location.world.spawn(location, TextDisplay::class.java) { display ->
                    display.text(Component.text("§7Recharging... ${minutes}m ${seconds}s"))
                    display.billboard = Display.Billboard.CENTER
                    display.brightness = Display.Brightness(15, 15)
                    
                    // Auto-remove after 1 second
                    plugin.server.scheduler.runTaskLater(plugin, Runnable {
                        FoliaUtil.runAtLocation(plugin, location) {
                            display.remove()
                        }
                    }, 20L)
                }
            }
        }
    }
}

