<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>custom-tiered-chests</artifactId>
    <groupId>com.tieredchests</groupId>
    <version>1.0.0</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>custom-tiered-chests-plugin</artifactId>
  <name>CustomTieredChests-Plugin</name>
  <build>
    <sourceDirectory>src/main/kotlin</sourceDirectory>
    <resources>
      <resource>
        <filtering>true</filtering>
        <directory>src/main/resources</directory>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-maven-plugin</artifactId>
        <version>${kotlin.version}</version>
        <executions>
          <execution>
            <id>compile</id>
            <phase>compile</phase>
            <goals>
              <goal>compile</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <jvmTarget>17</jvmTarget>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.5.1</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <relocations>
                <relocation>
                  <pattern>kotlin</pattern>
                  <shadedPattern>com.tieredchests.shaded.kotlin</shadedPattern>
                </relocation>
              </relocations>
              <filters>
                <filter>
                  <artifact>*:*</artifact>
                  <excludes>
                    <exclude>META-INF/*.SF</exclude>
                    <exclude>META-INF/*.DSA</exclude>
                    <exclude>META-INF/*.RSA</exclude>
                  </excludes>
                </filter>
              </filters>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <repositories>
    <repository>
      <id>papermc</id>
      <url>https://repo.papermc.io/repository/maven-public/</url>
    </repository>
    <repository>
      <id>placeholderapi</id>
      <url>https://repo.extendedclip.com/content/repositories/placeholderapi/</url>
    </repository>
    <repository>
      <id>sk89q-repo</id>
      <url>https://maven.enginehub.org/repo/</url>
    </repository>
    <repository>
      <id>jitpack.io</id>
      <url>https://jitpack.io</url>
    </repository>
  </repositories>
  <dependencies>
    <dependency>
      <groupId>io.papermc.paper</groupId>
      <artifactId>paper-api</artifactId>
      <version>1.20.4-R0.1-SNAPSHOT</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.yaml</groupId>
      <artifactId>snakeyaml</artifactId>
      <version>2.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>me.clip</groupId>
      <artifactId>placeholderapi</artifactId>
      <version>2.11.5</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.sk89q.worldguard</groupId>
      <artifactId>worldguard-bukkit</artifactId>
      <version>7.0.9</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.github.MilkBowl</groupId>
      <artifactId>VaultAPI</artifactId>
      <version>1.7</version>
      <scope>provided</scope>
    </dependency>
  </dependencies>
  <properties>
    <kotlin.version>1.9.22</kotlin.version>
  </properties>
</project>
