package com.tieredchests.manager

import com.tieredchests.TieredChestsPlugin
import org.bukkit.Bukkit
import org.bukkit.Material
import org.bukkit.configuration.ConfigurationSection
import org.bukkit.inventory.ItemStack
import org.bukkit.loot.LootContext
import org.bukkit.loot.LootTable
import java.util.*
import java.util.logging.Level
import kotlin.random.Random

class LootManager(private val plugin: TieredChestsPlugin) {
    
    private val customTables = mutableMapOf<String, CustomLootTable>()
    
    init {
        loadLootTables()
    }
    
    private fun loadLootTables() {
        customTables.clear()
        
        val tablesSection = plugin.configManager.lootTablesConfig.getConfigurationSection("tables")
            ?: return
        
        for (key in tablesSection.getKeys(false)) {
            val tableSection = tablesSection.getConfigurationSection(key) ?: continue
            
            try {
                val table = parseCustomTable(key, tableSection)
                customTables[key] = table
                plugin.logger.info("Loaded loot table: $key")
            } catch (e: Exception) {
                plugin.logger.log(Level.SEVERE, "Failed to load loot table: $key", e)
            }
        }
    }
    
    private fun parseCustomTable(id: String, section: ConfigurationSection): CustomLootTable {
        val rolls = section.getInt("rolls", 1)
        val pools = mutableListOf<LootPool>()
        
        val poolsList = section.getList("pools") ?: emptyList<Any>()
        for (poolData in poolsList) {
            if (poolData is Map<*, *>) {
                val weight = (poolData["weight"] as? Number)?.toInt() ?: 100
                val itemStr = poolData["item"] as? String ?: "minecraft:stone"
                val min = (poolData["min"] as? Number)?.toInt() ?: 1
                val max = (poolData["max"] as? Number)?.toInt() ?: 1
                
                val material = try {
                    Material.valueOf(itemStr.substringAfter(":").uppercase())
                } catch (e: Exception) {
                    Material.STONE
                }
                
                pools.add(LootPool(weight, material, min, max))
            }
        }
        
        return CustomLootTable(id, rolls, pools)
    }
    
    fun generateLoot(lootTableId: String, location: org.bukkit.Location): List<ItemStack> {
        // Check if it's a vanilla loot table
        if (lootTableId.startsWith("minecraft:")) {
            return generateVanillaLoot(lootTableId, location)
        }
        
        // Check custom tables
        val customTable = customTables[lootTableId]
        if (customTable != null) {
            return generateCustomLoot(customTable)
        }
        
        plugin.logger.warning("Unknown loot table: $lootTableId")
        return emptyList()
    }
    
    private fun generateVanillaLoot(lootTableId: String, location: org.bukkit.Location): List<ItemStack> {
        return try {
            val key = org.bukkit.NamespacedKey.minecraft(lootTableId.substringAfter("minecraft:"))
            val lootTable = Bukkit.getLootTable(key)

            if (lootTable != null) {
                val context = LootContext.Builder(location).build()
                lootTable.populateLoot(java.util.Random(), context).toList()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            plugin.logger.log(Level.WARNING, "Failed to generate vanilla loot: $lootTableId", e)
            emptyList()
        }
    }
    
    private fun generateCustomLoot(table: CustomLootTable): List<ItemStack> {
        val items = mutableListOf<ItemStack>()
        
        repeat(table.rolls) {
            val pool = selectWeightedPool(table.pools)
            if (pool != null) {
                val amount = Random.nextInt(pool.min, pool.max + 1)
                items.add(ItemStack(pool.material, amount))
            }
        }
        
        return items
    }
    
    private fun selectWeightedPool(pools: List<LootPool>): LootPool? {
        if (pools.isEmpty()) return null
        
        val totalWeight = pools.sumOf { it.weight }
        var random = Random.nextInt(totalWeight)
        
        for (pool in pools) {
            random -= pool.weight
            if (random < 0) {
                return pool
            }
        }
        
        return pools.last()
    }
    
    fun getLootTableIds(): List<String> {
        return customTables.keys.toList()
    }
    
    fun hasLootTable(id: String): Boolean {
        return customTables.containsKey(id) || id.startsWith("minecraft:")
    }
}

data class CustomLootTable(
    val id: String,
    val rolls: Int,
    val pools: List<LootPool>
)

data class LootPool(
    val weight: Int,
    val material: Material,
    val min: Int,
    val max: Int
)

