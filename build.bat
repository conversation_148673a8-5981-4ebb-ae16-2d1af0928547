@echo off
REM Build script for Custom Tiered Chests
REM Requires Maven to be installed

echo ========================================
echo Custom Tiered Chests - Build Script
echo ========================================
echo.

REM Set Maven path
set MAVEN_PATH=C:\Users\<USER>\Downloads\apache-maven-3.9.11\bin\mvn.cmd

REM Check if Maven exists
if not exist "%MAVEN_PATH%" (
    echo ERROR: Maven not found at %MAVEN_PATH%
    echo Please update MAVEN_PATH in this script
    pause
    exit /b 1
)

echo Using Maven: %MAVEN_PATH%
echo.

REM Clean and package
echo [1/3] Cleaning previous builds...
call "%MAVEN_PATH%" clean
if errorlevel 1 (
    echo ERROR: Clean failed!
    pause
    exit /b 1
)

echo.
echo [2/3] Compiling and packaging...
call "%MAVEN_PATH%" package
if errorlevel 1 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)

echo.
echo [3/3] Build complete!
echo.
echo ========================================
echo Output: plugin\target\custom-tiered-chests-plugin-1.0.0.jar
echo ========================================
echo.

pause

