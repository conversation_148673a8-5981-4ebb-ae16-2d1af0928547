# Troubleshooting Guide

## Recent Fixes (Build 1.0.0 - Latest)

### Fixed Issues

1. **Commands not working** ✅
   - **Problem**: Command required `ctc.admin` permission in plugin.yml, blocking all non-ops
   - **Fix**: Removed permission requirement from plugin.yml, permissions now checked inside command handler
   - **Result**: All players can now execute `/ctc` and see help, but admin commands still require permissions

2. **Tab completion not working** ✅
   - **Problem**: Tab completer was not registered
   - **Fix**: Added tab completer registration in TieredChestsPlugin.kt
   - **Result**: Tab completion now works for all subcommands

3. **GUI not showing loot tables correctly** ✅
   - **Problem**: Text extraction from Adventure Components was unreliable
   - **Fix**: Store loot table IDs in NBT (PersistentDataContainer) for reliable retrieval
   - **Result**: GUI now correctly identifies selected loot tables

4. **Better error handling** ✅
   - Added comprehensive logging throughout command execution
   - Added try-catch blocks in GUI opening
   - Added logging in placement listener
   - Better error messages for players

## Testing the Fixes

### 1. Test Commands

```bash
# Test basic command (should show help)
/ctc

# Test tab completion (press TAB after typing)
/ctc <TAB>

# Test givechest command (requires op or ctc.admin.givechest)
/ctc givechest <your-name> common 1

# Test givekey command (requires op or ctc.admin.givekey)
/ctc givekey <your-name> common
```

### 2. Test Chest Placement

1. **Give yourself a chest**:
   ```bash
   /ctc givechest <your-name> common 1
   ```

2. **Check your inventory** - You should have a chest item with custom name

3. **Place the chest** - Right-click to place it

4. **GUI should open** - You should see a GUI titled "Select Loot Table"

5. **Select a loot table** - Click any chest icon

6. **Confirmation message** - You should see "§aChest created with loot table: <table-id>"

### 3. Test Chest Opening

1. **Give yourself a key**:
   ```bash
   /ctc givekey <your-name> common
   ```

2. **Right-click the chest** with the key in hand

3. **Loot should appear** in your inventory

## Common Issues and Solutions

### Issue: "Unknown command" when typing `/ctc`

**Possible Causes:**
- Plugin not loaded
- Plugin failed to enable

**Solutions:**
1. Check server console for errors during plugin load
2. Verify plugin is in `plugins/` folder
3. Check `plugins/CustomTieredChests/` folder exists
4. Run `/plugins` to see if CustomTieredChests is green

### Issue: "No permission!" when running commands

**Possible Causes:**
- You're not OP
- You don't have the required permission

**Solutions:**
1. Make yourself OP: `/op <your-name>`
2. Or grant specific permission:
   ```bash
   /lp user <your-name> permission set ctc.admin true
   ```

### Issue: GUI not opening when placing chest

**Possible Causes:**
- Not using a tiered chest item
- Missing `ctc.place` permission
- Error in console

**Solutions:**
1. **Check console logs** - Look for:
   ```
   [CustomTieredChests] Player <name> is placing a tiered chest
   [CustomTieredChests] Setting block to <material> and opening GUI
   [CustomTieredChests] Opening loot table selection GUI for <name>
   ```

2. **Check permissions**:
   ```bash
   /lp user <your-name> permission set ctc.place true
   ```

3. **Verify you're using a tiered chest item**:
   - Item should have custom name (e.g., "§7Common Chest")
   - Item should have lore
   - Get a fresh one with `/ctc givechest`

4. **Check for errors**:
   - Look for stack traces in console
   - Check `logs/latest.log`

### Issue: Loot not generating

**Possible Causes:**
- Invalid loot table ID
- Chest on cooldown
- Missing key

**Solutions:**
1. **Inspect the chest**:
   ```bash
   /ctc inspect
   ```
   Then right-click the chest

2. **Check loot table exists**:
   - Custom tables: `plugins/CustomTieredChests/loot_tables.yml`
   - Vanilla tables: Must be valid Minecraft loot table

3. **Check cooldown**:
   - Inspect chest to see cooldown status
   - Wait for cooldown to expire

4. **Verify key**:
   - Key tier must match chest tier (or use instance key)
   - Key must have uses remaining

### Issue: Display entities not showing

**Possible Causes:**
- Chunk not loaded
- Entities despawned
- Server restart

**Solutions:**
1. **Rebuild shells**:
   ```bash
   /ctc rebuild
   ```

2. **Reload plugin**:
   ```bash
   /ctc reload
   ```

3. **Check entity count**:
   - Use `/minecraft:execute as @e[type=minecraft:block_display] run say hi`
   - Should see messages if entities exist

## Debug Mode

Enable debug logging in `plugins/CustomTieredChests/config.yml`:

```yaml
debug: true
```

Then reload:
```bash
/ctc reload
```

This will log detailed information about:
- Command execution
- GUI opening
- Chest placement
- Loot generation
- Shell spawning

## Console Log Examples

### Successful Chest Placement
```
[CustomTieredChests] Player Steve is placing a tiered chest
[CustomTieredChests] Setting block to CHEST and opening GUI
[CustomTieredChests] Opening loot table selection GUI for Steve
[CustomTieredChests] Player Steve selected loot table: starter_common
```

### Successful Chest Opening
```
[CustomTieredChests] Player Steve opened chest at (100, 64, 200)
[CustomTieredChests] Generating loot from table: starter_common
[CustomTieredChests] Generated 3 items
```

### Error Examples
```
[CustomTieredChests] [SEVERE] Failed to open GUI: <error message>
[CustomTieredChests] [WARNING] Invalid tier: unknown_tier
[CustomTieredChests] [WARNING] Clicked item has no loot table ID!
```

## Performance Issues

### Too many entities

**Symptoms:**
- Server lag
- Low TPS
- Many Display Entities

**Solutions:**
1. **Reduce max entities** in `config.yml`:
   ```yaml
   maxDisplayEntities: 5  # Default is 10
   ```

2. **Disable text badges**:
   ```yaml
   tiers:
     common:
       textBadge:
         enabled: false
   ```

3. **Clean up orphans**:
   ```bash
   /minecraft:kill @e[type=minecraft:block_display]
   /minecraft:kill @e[type=minecraft:item_display]
   /minecraft:kill @e[type=minecraft:text_display]
   /ctc rebuild
   ```

## Getting Help

If you're still experiencing issues:

1. **Check console logs** - Look for errors and warnings
2. **Enable debug mode** - Get detailed logging
3. **Test with fresh config** - Backup and delete `plugins/CustomTieredChests/`, restart server
4. **Check Paper version** - Must be 1.20.4 or higher
5. **Check for conflicts** - Disable other chest plugins temporarily

## Useful Commands for Debugging

```bash
# Check plugin status
/plugins

# Check permissions
/lp user <name> permission info

# List all entities
/minecraft:execute as @e run say hi

# Kill all display entities (WARNING: Removes ALL display entities on server)
/minecraft:kill @e[type=minecraft:block_display]
/minecraft:kill @e[type=minecraft:item_display]
/minecraft:kill @e[type=minecraft:text_display]

# Rebuild all chest shells
/ctc rebuild

# Reload plugin
/ctc reload

# Check server version
/version

# Check TPS
/tps
```

## File Locations

- **Plugin JAR**: `plugins/custom-tiered-chests-plugin-1.0.0.jar`
- **Config**: `plugins/CustomTieredChests/config.yml`
- **Tiers**: `plugins/CustomTieredChests/tiers.yml`
- **Loot Tables**: `plugins/CustomTieredChests/loot_tables.yml`
- **Chest Data**: `plugins/CustomTieredChests/chests/<uuid>.yml`
- **Logs**: `logs/latest.log`

## Next Steps After Testing

Once you've verified the fixes work:

1. **Customize tiers** - Edit `tiers.yml` to create unique visuals
2. **Add loot tables** - Edit `loot_tables.yml` to add custom loot
3. **Set permissions** - Configure who can place/use chests
4. **Create recipes** - Add crafting recipes for chests/keys (future feature)
5. **Add to world** - Place chests in dungeons, events, etc.

## Known Limitations

- Display Entities require Minecraft 1.20+
- Folia support is experimental
- Some vanilla loot tables may not work correctly
- Maximum 27 loot tables in GUI (can be extended)

## Report Issues

If you find bugs or have suggestions:
1. Note the exact error message
2. Include relevant console logs
3. Describe steps to reproduce
4. Include server version and plugin version

