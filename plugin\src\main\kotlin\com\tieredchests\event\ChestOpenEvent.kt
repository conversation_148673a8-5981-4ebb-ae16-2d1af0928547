package com.tieredchests.event

import com.tieredchests.model.ChestInstance
import com.tieredchests.model.TierDefinition
import org.bukkit.entity.Player
import org.bukkit.event.Cancellable
import org.bukkit.event.Event
import org.bukkit.event.HandlerList

class ChestOpenEvent(
    val player: Player,
    val chest: ChestInstance,
    val tier: TierDefinition
) : Event(), Cancellable {
    
    private var cancelled = false
    
    override fun isCancelled(): Boolean = cancelled
    
    override fun setCancelled(cancel: Boolean) {
        cancelled = cancel
    }
    
    override fun getHandlers(): HandlerList = handlerList
    
    companion object {
        private val handlerList = HandlerList()
        
        @JvmStatic
        fun getHandlerList(): HandlerList = handlerList
    }
}

