# Commands & Permissions

## Commands

All commands use the base `/ctc` (aliases: `/tieredchests`, `/tchests`)

### Admin Commands

#### Give Chest
```bash
/ctc givechest <player> <tier> [amount]
```
Give a tiered chest item to a player.

**Examples:**
```bash
/ctc givechest Steve common 1
/ctc givechest Alex mythic 5
```

**Permission:** `ctc.admin.givechest`

---

#### Give Key
```bash
/ctc givekey <player> <tier> [uses] [bind:tier|instance]
```
Give a key to a player.

**Parameters:**
- `uses`: Number of uses (omit for unlimited)
- `bind`: `tier` (any chest of tier) or `instance` (specific chest)

**Examples:**
```bash
/ctc givekey Steve common              # Unlimited tier key
/ctc givekey Alex rare 3                # 3-use tier key
/ctc givekey Bob mythic 1 instance      # 1-use instance key
```

**Permission:** `ctc.admin.givekey`

---

#### Set Loot Table
```bash
/ctc setloot <lootTableId>
```
Change the loot table of the chest you're looking at.

**Examples:**
```bash
/ctc setloot starter_common
/ctc setloot minecraft:chests/end_city_treasure
```

**Permission:** `ctc.admin.setloot`

---

#### Inspect
```bash
/ctc inspect
```
View detailed information about the chest you're looking at.

**Output:**
```
=== Chest Info ===
UUID: 123e4567-e89b-12d3-a456-************
Tier: mythic
Loot Table: mythic_end
Owner: Steve
Ready: false
Cooldown: 45m 23s
```

**Permission:** `ctc.admin.inspect`

---

#### Toggle Badge
```bash
/ctc togglebadge
```
Toggle the text badge on/off for the chest you're looking at.

**Permission:** `ctc.admin.badge`

---

#### Rebuild Shell
```bash
/ctc rebuild
```
Rebuild the visual shell (Display Entities) for the chest you're looking at.

**Use cases:**
- After config changes
- If entities disappeared
- After chunk corruption

**Permission:** `ctc.admin.rebuild`

---

#### Reload
```bash
/ctc reload
```
Reload all configuration files and rebuild all shells.

**Warning:** This will:
1. Save all current chest data
2. Reload configs
3. Rebuild all visual shells
4. May cause brief lag

**Permission:** `ctc.admin.reload`

---

## Permissions

### Admin Permissions

| Permission | Description | Default |
|------------|-------------|---------|
| `ctc.admin` | Full admin access (grants all below) | op |
| `ctc.admin.givechest` | Give chest items | op |
| `ctc.admin.givekey` | Give keys | op |
| `ctc.admin.setloot` | Change loot tables | op |
| `ctc.admin.inspect` | Inspect chests | op |
| `ctc.admin.badge` | Toggle badges | op |
| `ctc.admin.rebuild` | Rebuild shells | op |
| `ctc.admin.reload` | Reload configs | op |
| `ctc.admin.break` | Break any chest (even owned by others) | op |

### Player Permissions

| Permission | Description | Default |
|------------|-------------|---------|
| `ctc.use` | Use/open tiered chests | true |
| `ctc.place` | Place tiered chests | true |
| `ctc.break` | Break own tiered chests | true |

## Permission Examples

### LuckPerms

```bash
# Give all admin permissions
lp user Steve permission set ctc.admin true

# Give only chest-giving permission
lp user Alex permission set ctc.admin.givechest true

# Remove placement permission
lp user Bob permission set ctc.place false

# Group permissions
lp group moderator permission set ctc.admin.inspect true
lp group moderator permission set ctc.admin.rebuild true
```

### PermissionsEx

```bash
# Give all admin permissions
pex user Steve add ctc.admin

# Give specific permission
pex user Alex add ctc.admin.givechest

# Group permissions
pex group moderator add ctc.admin.inspect
pex group moderator add ctc.admin.rebuild
```

## Tab Completion

The plugin provides smart tab completion:

```bash
/ctc <TAB>
  → givechest, givekey, setloot, inspect, togglebadge, rebuild, reload

/ctc givechest <TAB>
  → [online players]

/ctc givechest Steve <TAB>
  → common, rare, epic, mythic

/ctc givekey Steve rare <TAB>
  → [number for uses]

/ctc setloot <TAB>
  → [available loot table IDs]
```

## Command Aliases

| Full Command | Aliases |
|--------------|---------|
| `/ctc` | `/tieredchests`, `/tchests` |
| `/ctc setloot` | `/ctc setloottable` |
| `/ctc rebuild` | `/ctc rebuildshell` |

## Usage Tips

1. **Batch operations**: Use a script or command block for bulk operations
2. **Testing**: Use `/ctc inspect` to verify chest configuration
3. **Debugging**: Enable `debug: true` in config.yml for verbose logging
4. **Recovery**: Use `/ctc rebuild` if visual shells disappear after crashes
5. **Performance**: Avoid `/ctc reload` during peak hours (causes brief lag)

## Common Workflows

### Setting up a new chest type

```bash
# 1. Edit tiers.yml to add new tier
# 2. Reload config
/ctc reload

# 3. Give yourself a chest
/ctc givechest @s custom_tier 1

# 4. Place and configure
# 5. Test with a key
/ctc givekey @s custom_tier
```

### Fixing broken visuals

```bash
# For one chest
/ctc rebuild

# For all chests
/ctc reload
```

### Creating event rewards

```bash
# Give winner a mythic chest with unlimited key
/ctc givechest WinnerName mythic 1
/ctc givekey WinnerName mythic
```

