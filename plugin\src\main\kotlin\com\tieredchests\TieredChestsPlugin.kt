package com.tieredchests

import com.tieredchests.api.TieredChestsAPI
import com.tieredchests.commands.CTCCommand
import com.tieredchests.config.ConfigManager
import com.tieredchests.listeners.*
import com.tieredchests.manager.*
import com.tieredchests.integration.IntegrationManager
import org.bukkit.plugin.java.JavaPlugin
import java.util.logging.Level

class TieredChestsPlugin : JavaPlugin() {
    
    lateinit var configManager: ConfigManager
        private set
    
    lateinit var tierManager: TierManager
        private set
    
    lateinit var chestManager: ChestManager
        private set
    
    lateinit var keyManager: KeyManager
        private set
    
    lateinit var lootManager: LootManager
        private set
    
    lateinit var shellManager: ShellManager
        private set
    
    lateinit var integrationManager: IntegrationManager
        private set
    
    lateinit var api: TieredChestsAPI
        private set
    
    override fun onEnable() {
        instance = this
        
        try {
            // Initialize managers
            logger.info("Initializing Custom Tiered Chests...")
            
            configManager = ConfigManager(this)
            configManager.loadAll()
            
            tierManager = TierManager(this)
            lootManager = LootManager(this)
            keyManager = KeyManager(this)
            shellManager = ShellManager(this)
            chestManager = ChestManager(this)
            integrationManager = IntegrationManager(this)
            
            // Initialize API
            api = TieredChestsAPI(this)
            
            // Register commands
            getCommand("ctc")?.setExecutor(CTCCommand(this))
            
            // Register listeners
            registerListeners()
            
            // Load data
            chestManager.loadAll()
            
            // Start periodic tasks
            startPeriodicTasks()
            
            logger.info("Custom Tiered Chests v${description.version} enabled!")
            logger.info("Loaded ${tierManager.getTiers().size} tiers")
            logger.info("Loaded ${chestManager.getAllChests().size} chest instances")
            
        } catch (e: Exception) {
            logger.log(Level.SEVERE, "Failed to enable Custom Tiered Chests!", e)
            server.pluginManager.disablePlugin(this)
        }
    }
    
    override fun onDisable() {
        try {
            // Save all data
            chestManager.saveAll()
            
            // Clean up shells
            shellManager.cleanupAll()
            
            logger.info("Custom Tiered Chests disabled!")
        } catch (e: Exception) {
            logger.log(Level.SEVERE, "Error during shutdown!", e)
        }
    }
    
    private fun registerListeners() {
        val pm = server.pluginManager
        pm.registerEvents(ChestPlaceListener(this), this)
        pm.registerEvents(ChestBreakListener(this), this)
        pm.registerEvents(ChestInteractListener(this), this)
        pm.registerEvents(ChestLoadListener(this), this)
        pm.registerEvents(InventoryListener(this), this)
    }
    
    private fun startPeriodicTasks() {
        // Orphan shell cleanup every 5 minutes
        server.scheduler.runTaskTimerAsynchronously(this, Runnable {
            shellManager.cleanupOrphans()
        }, 6000L, 6000L)
        
        // Respawn check every minute
        server.scheduler.runTaskTimer(this, Runnable {
            chestManager.processRespawns()
        }, 1200L, 1200L)
    }
    
    fun reload() {
        try {
            // Save current state
            chestManager.saveAll()
            
            // Reload configs
            configManager.loadAll()
            
            // Reload managers
            tierManager = TierManager(this)
            lootManager = LootManager(this)
            
            // Rebuild all shells
            chestManager.rebuildAllShells()
            
            logger.info("Configuration reloaded successfully!")
        } catch (e: Exception) {
            logger.log(Level.SEVERE, "Failed to reload configuration!", e)
            throw e
        }
    }
    
    companion object {
        lateinit var instance: TieredChestsPlugin
            private set
    }
}

