# Permissions Reference

## Permission Hierarchy

```
ctc.admin (op)
├── ctc.admin.givechest (op)
├── ctc.admin.givekey (op)
├── ctc.admin.setloot (op)
├── ctc.admin.inspect (op)
├── ctc.admin.badge (op)
├── ctc.admin.rebuild (op)
├── ctc.admin.reload (op)
└── ctc.admin.break (op)

ctc.use (true)
ctc.place (true)
ctc.break (true)
```

## Admin Permissions

### ctc.admin
**Default:** op  
**Description:** Grants all admin permissions  
**Includes:** All permissions below

### ctc.admin.givechest
**Default:** op  
**Description:** Give tiered chest items to players  
**Command:** `/ctc givechest <player> <tier> [amount]`

### ctc.admin.givekey
**Default:** op  
**Description:** Give keys to players  
**Command:** `/ctc givekey <player> <tier> [uses] [bind]`

### ctc.admin.setloot
**Default:** op  
**Description:** Change loot tables of existing chests  
**Command:** `/ctc setloot <lootTableId>`

### ctc.admin.inspect
**Default:** op  
**Description:** View detailed chest information  
**Command:** `/ctc inspect`

### ctc.admin.badge
**Default:** op  
**Description:** Toggle text badges on chests  
**Command:** `/ctc togglebadge`

### ctc.admin.rebuild
**Default:** op  
**Description:** Rebuild visual shells  
**Command:** `/ctc rebuild`

### ctc.admin.reload
**Default:** op  
**Description:** Reload plugin configuration  
**Command:** `/ctc reload`

### ctc.admin.break
**Default:** op  
**Description:** Break any chest, even if owned by another player  
**Usage:** Automatic when breaking chests

## Player Permissions

### ctc.use
**Default:** true  
**Description:** Open and use tiered chests  
**Usage:** Automatic when right-clicking chests

### ctc.place
**Default:** true  
**Description:** Place tiered chests  
**Usage:** Automatic when placing chest items

### ctc.break
**Default:** true  
**Description:** Break own tiered chests  
**Usage:** Automatic when breaking chests

## Permission Examples

### LuckPerms

#### Give full admin access
```bash
lp user Steve permission set ctc.admin true
```

#### Give specific permissions
```bash
lp user Alex permission set ctc.admin.givechest true
lp user Alex permission set ctc.admin.givekey true
```

#### Create moderator role
```bash
lp creategroup moderator
lp group moderator permission set ctc.admin.inspect true
lp group moderator permission set ctc.admin.rebuild true
lp group moderator permission set ctc.admin.badge true
lp user Bob parent add moderator
```

#### Restrict placement
```bash
lp user Griefer permission set ctc.place false
```

#### VIP group with unlimited use
```bash
lp creategroup vip
lp group vip permission set ctc.use true
lp group vip permission set ctc.place true
lp group vip permission set ctc.break true
```

### PermissionsEx

#### Give full admin access
```bash
pex user Steve add ctc.admin
```

#### Give specific permissions
```bash
pex user Alex add ctc.admin.givechest
pex user Alex add ctc.admin.givekey
```

#### Create moderator group
```bash
pex group moderator add ctc.admin.inspect
pex group moderator add ctc.admin.rebuild
pex group moderator add ctc.admin.badge
pex user Bob group add moderator
```

#### Restrict placement
```bash
pex user Griefer add ctc.place false
```

### GroupManager

#### Give full admin access
```bash
manuaddp Steve ctc.admin
```

#### Give specific permissions
```bash
manuaddp Alex ctc.admin.givechest
manuaddp Alex ctc.admin.givekey
```

#### Create moderator group
```bash
mangaddp moderator ctc.admin.inspect
mangaddp moderator ctc.admin.rebuild
mangaddp moderator ctc.admin.badge
manuaddsub Bob moderator
```

## Permission Use Cases

### Server Owner
```yaml
permissions:
  - ctc.admin
```
Full control over all plugin features.

### Admin/Moderator
```yaml
permissions:
  - ctc.admin.inspect
  - ctc.admin.rebuild
  - ctc.admin.badge
  - ctc.admin.break
```
Can manage chests and help players, but can't create new ones.

### Event Manager
```yaml
permissions:
  - ctc.admin.givechest
  - ctc.admin.givekey
  - ctc.admin.inspect
```
Can give rewards and check chest status.

### Builder
```yaml
permissions:
  - ctc.place
  - ctc.break
  - ctc.use
```
Can place and use chests in creative builds.

### Regular Player
```yaml
permissions:
  - ctc.use
  - ctc.place
  - ctc.break
```
Normal gameplay permissions (default).

### Restricted Player
```yaml
permissions:
  - ctc.use
  - ctc.place: false
  - ctc.break: false
```
Can only open chests, not place or break them.

## Permission Checks

The plugin checks permissions at these points:

1. **Command execution**: When running `/ctc` commands
2. **Chest placement**: When placing a tiered chest item
3. **Chest opening**: When right-clicking a chest (optional, see config)
4. **Chest breaking**: When breaking a tiered chest block
5. **GUI interaction**: When selecting loot tables

## Troubleshooting

### "You don't have permission!"

1. Check your permission plugin is loaded:
```bash
/plugins
```

2. Verify the permission is set:
```bash
/lp user <name> permission check ctc.admin
```

3. Check for negations:
```bash
/lp user <name> permission info
```

4. Ensure inheritance is correct:
```bash
/lp user <name> parent info
```

### Permission not working

1. Reload permissions:
```bash
/lp sync
```

2. Check for conflicts:
```bash
/lp user <name> permission check ctc.use
```

3. Verify default permissions in `plugin.yml`

4. Check server logs for errors

### Ops not working

If ops don't have permissions:

1. Check `server.properties`:
```properties
op-permission-level=4
```

2. Verify op status:
```bash
/op <player>
```

3. Check permission plugin settings for op override

## Best Practices

1. **Use groups**: Don't assign permissions individually
2. **Principle of least privilege**: Only give needed permissions
3. **Test in creative**: Verify permissions before production
4. **Document changes**: Keep track of custom permission setups
5. **Regular audits**: Review who has admin permissions

## Integration with Other Plugins

### WorldGuard

Combine with region flags:
```bash
/rg flag spawn ctc-place deny
/rg flag spawn ctc-break deny
```

### Vault

Economy permissions work automatically if Vault is installed.

### PlaceholderAPI

No special permissions needed for placeholders.

## Default Permission File

If using a permissions plugin, here's a starter config:

```yaml
groups:
  default:
    permissions:
      - ctc.use
      - ctc.place
      - ctc.break
  
  moderator:
    permissions:
      - ctc.admin.inspect
      - ctc.admin.rebuild
      - ctc.admin.badge
      - ctc.admin.break
  
  admin:
    permissions:
      - ctc.admin
```

