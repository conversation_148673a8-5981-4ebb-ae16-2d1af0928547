package com.tieredchests.model

import org.bukkit.Location
import java.util.*

data class ChestInstance(
    val uuid: UUID,
    val location: Location,
    val tierId: String,
    val lootTableId: String,
    val owner: UUID?,
    val placedAt: Long,
    var lastOpened: Long?,
    var nextRespawn: Long?,
    var firstOpenDone: Boolean,
    var shellEntities: List<UUID>,
    var badgeEnabled: Boolean
) {
    fun isReady(): Boolean {
        return nextRespawn == null || System.currentTimeMillis() >= nextRespawn!!
    }
    
    fun getRemainingCooldown(): Long {
        return if (nextRespawn == null) 0L else maxOf(0L, nextRespawn!! - System.currentTimeMillis())
    }
}

