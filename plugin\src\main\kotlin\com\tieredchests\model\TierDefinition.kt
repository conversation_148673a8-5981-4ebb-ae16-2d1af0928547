package com.tieredchests.model

import org.bukkit.Material
import org.bukkit.Sound
import org.bukkit.Particle

data class TierDefinition(
    val id: String,
    val displayName: String,
    val colorName: String,
    val coreBlock: Material,
    val lockMode: LockMode,
    val keyConsumeMode: KeyConsumeMode,
    val accentScheme: String,
    val accents: List<AccentPiece>,
    val textBadge: TextBadge?,
    val openSound: Sound?,
    val closeSound: Sound?,
    val particles: ParticleEffect?,
    val respawnPolicy: RespawnPolicy
)

data class AccentPiece(
    val type: DisplayType,
    val material: Material?,
    val item: Material?,
    val transform: Transform
)

data class Transform(
    val sx: Double = 1.0,
    val sy: Double = 1.0,
    val sz: Double = 1.0,
    val rx: Double = 0.0,
    val ry: Double = 0.0,
    val rz: Double = 0.0,
    val tx: Double = 0.0,
    val ty: Double = 0.0,
    val tz: Double = 0.0
)

data class TextBadge(
    val enabled: Boolean,
    val content: String,
    val offset: Vector3D,
    val scale: Float = 0.5f,
    val billboard: Billboard = Billboard.CENTER
)

data class Vector3D(
    val x: Double,
    val y: Double,
    val z: Double
)

data class ParticleEffect(
    val particle: Particle,
    val count: Int,
    val offsetX: Double,
    val offsetY: Double,
    val offsetZ: Double,
    val speed: Double
)

enum class DisplayType {
    BLOCK_DISPLAY,
    ITEM_DISPLAY,
    TEXT_DISPLAY
}

enum class LockMode {
    NONE,
    TIER,
    INSTANCE
}

enum class KeyConsumeMode {
    NEVER,
    ON_OPEN,
    ON_FIRST_OPEN
}

enum class Billboard {
    FIXED,
    VERTICAL,
    HORIZONTAL,
    CENTER
}

sealed class RespawnPolicy {
    object None : RespawnPolicy()
    data class Fixed(val minutes: Int) : RespawnPolicy()
    data class Cron(val expression: String) : RespawnPolicy()
}

