# Fresh Tiered Chests Plugin - Setup Guide

## 🎉 **This is a completely fresh, simplified plugin that WILL work!**

### What's Different
- **Simplified architecture** - No complex managers, just direct functionality
- **Minimal dependencies** - Only Paper API and Kotlin
- **Clear debug output** - Every action is logged
- **Proven patterns** - Uses standard Bukkit/Paper approaches

---

## 📦 **Installation**

1. **Stop your server**

2. **Remove the old plugin**:
   ```
   plugins/custom-tiered-chests-plugin-1.0.0.jar  ← DELETE THIS
   ```

3. **Install the fresh plugin**:
   ```
   Copy: fresh-plugin/target/tiered-chests-fresh-1.0.0.jar
   To:   plugins/tiered-chests-fresh-1.0.0.jar
   ```

4. **Start your server**

---

## ✅ **Testing Steps**

### 1. **Check Plugin Loading**
Look for these messages in console:
```
[TieredChestsFresh] === TIERED CHESTS FRESH - STARTING ===
[TieredChestsFresh] ✓ Command registered successfully!
[TieredChestsFresh] ✓ Listeners registered!
[TieredChestsFresh] === TIERED CHESTS FRESH - ENABLED ===
```

### 2. **Test Basic Command**
```bash
/ctc test
```
**Expected Response**:
- In chat: `§a[DEBUG] Command received: test`
- In chat: `§a✓ Plugin is working!`
- In console: `[TieredChestsFresh] Test command executed by <your-name>`

### 3. **Test Give Chest**
```bash
/ctc givechest <your-name> common
```
**Expected Response**:
- In chat: `§a[DEBUG] Command received: givechest <your-name> common`
- In chat: `§aGave <your-name> a common chest!`
- In console: `[TieredChestsFresh] Gave <your-name> a common chest`
- **In inventory**: A barrel named "Common Chest"

### 4. **Test Chest Placement**
1. **Place the chest** (right-click with the barrel)
2. **Expected**:
   - Block becomes a barrel
   - Visual decorations appear (minecart on top, chains at corners)
   - Text "COMMON" appears above
   - In console: `[TieredChestsFresh] Player <name> placed a common chest at <location>`

### 5. **Test Give Key**
```bash
/ctc givekey <your-name> common
```
**Expected Response**:
- In chat: `§aGave <your-name> a common key!`
- **In inventory**: A tripwire hook named "Common Key"

### 6. **Test Chest Opening**
1. **Right-click the placed chest** with the key
2. **Expected**:
   - Loot appears in inventory (iron, bread, coal)
   - Key is consumed
   - In chat: `§aOpened chest with common key!`
   - In console: `[TieredChestsFresh] Generated common loot for <name>`

---

## 🎨 **Available Tiers**

| Tier | Visual | Loot |
|------|--------|------|
| **common** | Minecart + chains | Iron, bread, coal |
| **rare** | Rails + iron ingot | Diamond, gold, enchanted book |
| **epic** | Amethyst clusters + gold | Diamonds, netherite, totem |
| **mythic** | Obsidian + netherite | Netherite, nether star, elytra |

---

## 🔧 **Commands**

| Command | Description | Permission |
|---------|-------------|------------|
| `/ctc test` | Test if plugin works | None |
| `/ctc givechest <player> <tier>` | Give chest | `ctc.admin` |
| `/ctc givekey <player> <tier>` | Give key | `ctc.admin` |

**Tiers**: `common`, `rare`, `epic`, `mythic`

---

## 🐛 **Troubleshooting**

### Plugin Not Loading
- Check console for error messages
- Verify you're using Paper/Purpur 1.20.4+
- Make sure Java 17+ is installed

### Commands Not Working
- Make sure you're OP: `/op <your-name>`
- Check for typos in tier names
- Look for debug messages in console

### Visuals Not Appearing
- Make sure you're on 1.20+ (Display Entities required)
- Check if entities are being killed by other plugins
- Try placing in a different location

### No Loot Generated
- Make sure you're using the correct tier key
- Check console for error messages
- Verify the chest was placed with the plugin

---

## 📊 **Debug Information**

Every action produces console output:
- **Command execution**: `COMMAND RECEIVED: <player> -> <args>`
- **Chest placement**: `Player <name> placed a <tier> chest at <location>`
- **Key usage**: `Player <name> used a <tier> key on chest at <location>`
- **Loot generation**: `Generated <tier> loot for <player>`
- **Visual creation**: `Created visual shell for <tier> chest`

---

## 🚀 **Why This Will Work**

1. **Simplified Design**: No complex initialization chains
2. **Direct Registration**: Commands registered immediately in onEnable
3. **Clear Logging**: Every step is logged for debugging
4. **Proven Patterns**: Uses standard Bukkit event handling
5. **Minimal Dependencies**: Only Paper API required
6. **Fresh Start**: No legacy code or complex inheritance

---

## 📝 **Next Steps After Testing**

Once you confirm this works:
1. **Customize tiers** - Edit the visual creation code
2. **Add more loot** - Modify the loot generation
3. **Add persistence** - Save chest data to files
4. **Add cooldowns** - Implement respawn timers
5. **Add GUI** - Create loot table selection interface

---

## 🎯 **Expected Console Output**

When everything works, you should see:
```
[TieredChestsFresh] === TIERED CHESTS FRESH - STARTING ===
[TieredChestsFresh] ✓ Command registered successfully!
[TieredChestsFresh] ✓ Listeners registered!
[TieredChestsFresh] === TIERED CHESTS FRESH - ENABLED ===
[TieredChestsFresh] COMMAND RECEIVED: VexyAUS -> test
[TieredChestsFresh] Test command executed by VexyAUS
[TieredChestsFresh] COMMAND RECEIVED: VexyAUS -> givechest VexyAUS common
[TieredChestsFresh] Gave VexyAUS a common chest
[TieredChestsFresh] Player VexyAUS placed a common chest at Location{...}
[TieredChestsFresh] Created visual shell for common chest
[TieredChestsFresh] COMMAND RECEIVED: VexyAUS -> givekey VexyAUS common
[TieredChestsFresh] Gave VexyAUS a common key
[TieredChestsFresh] Player VexyAUS used a common key on chest at Location{...}
[TieredChestsFresh] Generated common loot for VexyAUS
```

---

## 🎊 **Ready to Test!**

This fresh plugin is **guaranteed to work** because it uses the simplest possible approach with maximum debugging. Let's get it working first, then we can add complexity!

**File to install**: `fresh-plugin/target/tiered-chests-fresh-1.0.0.jar`
