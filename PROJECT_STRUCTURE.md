# Project Structure

## Overview

```
custom-tiered-chests/
├── pom.xml                          # Root Maven POM (multi-module)
├── build.bat                        # Windows build script
├── README.md                        # Main documentation
├── GETTING_STARTED.md               # Quick start guide
├── LICENSE                          # MIT License
├── .gitignore                       # Git ignore rules
│
├── plugin/                          # Main plugin module
│   ├── pom.xml                      # Plugin Maven POM
│   ├── src/main/
│   │   ├── kotlin/com/tieredchests/
│   │   │   ├── TieredChestsPlugin.kt           # Main plugin class
│   │   │   │
│   │   │   ├── api/
│   │   │   │   └── TieredChestsAPI.kt          # Public API
│   │   │   │
│   │   │   ├── commands/
│   │   │   │   └── CTCCommand.kt               # Command handler
│   │   │   │
│   │   │   ├── config/
│   │   │   │   └── ConfigManager.kt            # Configuration management
│   │   │   │
│   │   │   ├── event/
│   │   │   │   └── ChestOpenEvent.kt           # Custom events
│   │   │   │
│   │   │   ├── gui/
│   │   │   │   └── LootTableSelectionGUI.kt    # Loot selection GUI
│   │   │   │
│   │   │   ├── integration/
│   │   │   │   ├── IntegrationManager.kt       # Integration coordinator
│   │   │   │   └── PlaceholderAPIIntegration.kt # PAPI support
│   │   │   │
│   │   │   ├── listeners/
│   │   │   │   ├── ChestPlaceListener.kt       # Placement handler
│   │   │   │   ├── ChestBreakListener.kt       # Break handler
│   │   │   │   ├── ChestInteractListener.kt    # Interaction handler
│   │   │   │   ├── ChestLoadListener.kt        # Chunk load handler
│   │   │   │   └── InventoryListener.kt        # Inventory handler
│   │   │   │
│   │   │   ├── manager/
│   │   │   │   ├── ChestManager.kt             # Chest instance management
│   │   │   │   ├── KeyManager.kt               # Key creation/validation
│   │   │   │   ├── LootManager.kt              # Loot generation
│   │   │   │   ├── ShellManager.kt             # Display Entity visuals
│   │   │   │   └── TierManager.kt              # Tier definitions
│   │   │   │
│   │   │   ├── model/
│   │   │   │   ├── TierDefinition.kt           # Tier data model
│   │   │   │   ├── ChestInstance.kt            # Chest data model
│   │   │   │   └── KeyData.kt                  # Key data model
│   │   │   │
│   │   │   └── util/
│   │   │       └── FoliaUtil.kt                # Folia compatibility
│   │   │
│   │   └── resources/
│   │       ├── plugin.yml                      # Plugin metadata
│   │       ├── config.yml                      # Default config
│   │       ├── tiers.yml                       # Default tiers
│   │       └── loot_tables.yml                 # Default loot tables
│   │
│   └── target/
│       └── custom-tiered-chests-plugin-1.0.0.jar  # Built JAR
│
├── docs/                            # Documentation
│   ├── README.md                    # Plugin overview
│   ├── CONFIG.md                    # Configuration guide
│   ├── COMMANDS.md                  # Commands reference
│   ├── LOOT_TABLES.md               # Loot table guide
│   └── PERMISSIONS.md               # Permissions reference
│
└── examples/                        # Example configurations
    └── config/
        ├── tiers.yml                # Example tier configs
        └── loot_tables.yml          # Example loot configs
```

## Key Components

### Core Plugin (`TieredChestsPlugin.kt`)
- Main entry point
- Manager initialization
- Event registration
- Lifecycle management

### Managers

#### ChestManager
- Create/delete chest instances
- Persistence (YAML files)
- Location indexing
- Respawn processing

#### ShellManager
- Spawn Display Entities
- Build visual shells
- Rebuild on chunk load
- Cleanup orphaned entities

#### TierManager
- Load tier definitions
- Parse accent configurations
- Validate tier data

#### KeyManager
- Create keys with NBT
- Validate key access
- Consume uses
- Bind to instances

#### LootManager
- Generate loot from tables
- Support vanilla tables
- Weighted pool selection
- Custom loot parsing

### Data Models

#### TierDefinition
- Visual configuration
- Lock/consume modes
- Respawn policies
- Accent pieces

#### ChestInstance
- Location and UUID
- Tier and loot table
- Cooldown state
- Shell entity IDs

#### KeyData
- Tier binding
- Instance binding
- Use counter
- Bind mode

### Utilities

#### FoliaUtil
- Region-thread safety
- Async task scheduling
- Location-based execution
- Bukkit/Folia compatibility

## Build Output

### JAR Structure
```
custom-tiered-chests-plugin-1.0.0.jar
├── com/tieredchests/              # Plugin classes
├── kotlin/                        # Shaded Kotlin stdlib
├── META-INF/
│   └── MANIFEST.MF
├── plugin.yml
├── config.yml
├── tiers.yml
└── loot_tables.yml
```

### Runtime Files
```
plugins/CustomTieredChests/
├── config.yml                     # Active config
├── tiers.yml                      # Active tiers
├── loot_tables.yml                # Active loot tables
└── chests/                        # Chest instances
    ├── <uuid-1>.yml
    ├── <uuid-2>.yml
    └── ...
```

## Dependencies

### Compile-time
- **Paper API** 1.20.4-R0.1-SNAPSHOT (provided)
- **Kotlin stdlib** 1.9.22 (shaded)

### Optional (provided)
- **PlaceholderAPI** 2.11.5
- **WorldGuard** 7.0.9
- **Vault** 1.7

## Build Process

1. **Clean**: Remove old builds
2. **Compile Kotlin**: Compile source files
3. **Process Resources**: Copy configs
4. **Package**: Create JAR
5. **Shade**: Include Kotlin stdlib
6. **Relocate**: Shade Kotlin packages

## Configuration Flow

```
Server Start
    ↓
Load config.yml
    ↓
Load tiers.yml → TierManager
    ↓
Load loot_tables.yml → LootManager
    ↓
Load chests/*.yml → ChestManager
    ↓
Rebuild shells → ShellManager
    ↓
Ready!
```

## Event Flow

### Placement
```
Player places chest item
    ↓
ChestPlaceListener
    ↓
Open loot table GUI
    ↓
Player selects table
    ↓
ChestManager.createChest()
    ↓
ShellManager.spawnShell()
    ↓
Save to chests/<uuid>.yml
```

### Opening
```
Player right-clicks chest
    ↓
ChestInteractListener
    ↓
Validate key
    ↓
Check cooldown
    ↓
Fire ChestOpenEvent
    ↓
LootManager.generateLoot()
    ↓
Give items to player
    ↓
Update cooldown
    ↓
Save chest data
```

### Chunk Load
```
Chunk loads
    ↓
ChestLoadListener
    ↓
Find chests in chunk
    ↓
Check shell entities exist
    ↓
Rebuild if missing
    ↓
Save updated data
```

## API Usage

### External Plugins
```java
// Get API
TieredChestsAPI api = TieredChestsPlugin.getInstance().getApi();

// Get chest
ChestInstance chest = api.getChest(location);

// Give key
api.giveKey(player, "mythic", 3, KeyBindMode.TIER);

// Register tier
TierDefinition tier = new TierDefinition(...);
api.registerTier(tier);
```

## Performance Considerations

### Async Operations
- File I/O (save/load)
- Orphan cleanup
- Batch operations

### Sync Operations
- Display Entity spawning
- Loot generation
- Player interactions

### Optimization
- Location indexing (HashMap)
- Lazy shell rebuilding
- Configurable entity limits
- Batch saves

## Testing Checklist

- [ ] Place chest → GUI opens
- [ ] Select loot table → Chest created
- [ ] Open with key → Loot generated
- [ ] Cooldown → Prevents opening
- [ ] Chunk unload/load → Shell persists
- [ ] Server restart → Data persists
- [ ] /ctc reload → Shells rebuild
- [ ] Break chest → Shell removed
- [ ] Multiple tiers → Unique visuals
- [ ] Vanilla loot tables → Work correctly

## Maintenance

### Adding New Tiers
1. Edit `tiers.yml`
2. Define accents
3. Test visuals
4. Reload plugin

### Adding New Loot
1. Edit `loot_tables.yml`
2. Define pools
3. Test generation
4. Reload plugin

### Debugging
1. Enable `debug: true` in config.yml
2. Check console logs
3. Use `/ctc inspect`
4. Check `chests/*.yml` files

## Future Enhancements

- [ ] MySQL/PostgreSQL support
- [ ] Web dashboard
- [ ] Hologram cooldowns
- [ ] Crafting recipes
- [ ] More accent schemes
- [ ] Ambient effects
- [ ] Multi-language support
- [ ] Statistics tracking

