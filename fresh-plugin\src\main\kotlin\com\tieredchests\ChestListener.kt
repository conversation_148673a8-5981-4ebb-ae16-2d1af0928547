package com.tieredchests

import net.kyori.adventure.text.Component
import net.kyori.adventure.text.format.NamedTextColor
import org.bukkit.Bukkit
import org.bukkit.Location
import org.bukkit.Material
import org.bukkit.NamespacedKey
import org.bukkit.entity.BlockDisplay
import org.bukkit.entity.ItemDisplay
import org.bukkit.entity.TextDisplay
import org.bukkit.event.EventHandler
import org.bukkit.event.Listener
import org.bukkit.event.block.BlockPlaceEvent
import org.bukkit.event.player.PlayerInteractEvent
import org.bukkit.inventory.ItemStack
import org.bukkit.persistence.PersistentDataType
import org.bukkit.util.Transformation
import org.joml.AxisAngle4f
import org.joml.Vector3f
import java.util.*

class ChestListener(private val plugin: TieredChestsPlugin) : Listener {
    
    private val chestKey = NamespacedKey(plugin, "tiered_chest")
    private val tierKey = NamespacedKey(plugin, "tier")
    private val keyKey = NamespacedKey(plugin, "key")
    private val chestIdKey = NamespacedKey(plugin, "chest_id")

    private fun getColorCode(color: NamedTextColor): String {
        return when (color) {
            NamedTextColor.GRAY -> "7"
            NamedTextColor.BLUE -> "9"
            NamedTextColor.LIGHT_PURPLE -> "d"
            NamedTextColor.GOLD -> "6"
            else -> "f"
        }
    }
    
    @EventHandler
    fun onPlace(event: BlockPlaceEvent) {
        val item = event.itemInHand
        if (!item.hasItemMeta()) return
        
        val meta = item.itemMeta
        if (!meta.persistentDataContainer.has(chestKey, PersistentDataType.BOOLEAN)) return
        
        val tier = meta.persistentDataContainer.get(tierKey, PersistentDataType.STRING) ?: return
        
        plugin.logger.info("Player ${event.player.name} placed a $tier chest at ${event.blockPlaced.location}")
        
        // Set block type
        event.blockPlaced.type = Material.BARREL
        
        // Create visual shell
        createShell(event.blockPlaced.location, tier)
        
        // Store chest data in block
        val blockMeta = event.blockPlaced.getState()
        if (blockMeta is org.bukkit.block.data.BlockData) {
            // We'll use a simple approach - store in a map for now
            val chestId = UUID.randomUUID().toString()
            // In a real plugin, you'd save this to a file or database
            plugin.logger.info("Created chest with ID: $chestId")
        }
        
        event.player.sendMessage("§aPlaced a $tier chest!")
    }
    
    @EventHandler
    fun onInteract(event: PlayerInteractEvent) {
        val block = event.clickedBlock ?: return
        if (block.type != Material.BARREL) return
        
        val item = event.item ?: return
        if (!item.hasItemMeta()) return
        
        val meta = item.itemMeta
        if (!meta.persistentDataContainer.has(keyKey, PersistentDataType.BOOLEAN)) return
        
        val keyTier = meta.persistentDataContainer.get(tierKey, PersistentDataType.STRING) ?: return
        
        event.isCancelled = true
        
        plugin.logger.info("Player ${event.player.name} used a $keyTier key on chest at ${block.location}")
        
        // Generate loot
        generateLoot(event.player, keyTier, block.location)
        
        // Consume key
        if (item.amount > 1) {
            item.amount = item.amount - 1
        } else {
            event.player.inventory.setItemInMainHand(null)
        }
        
        event.player.sendMessage("§aOpened chest with $keyTier key!")
    }
    
    private fun createShell(location: Location, tier: String) {
        val world = location.world ?: return
        
        // Create different visuals based on tier
        when (tier.lowercase()) {
            "common" -> {
                // Minecart on top
                val minecart = world.spawn(location.clone().add(0.5, 0.7, 0.5), ItemDisplay::class.java)
                minecart.setItemStack(ItemStack(Material.MINECART))
                minecart.transformation = Transformation(
                    Vector3f(0f, 0f, 0f),
                    AxisAngle4f(Math.toRadians(-90.0).toFloat(), 1f, 0f, 0f),
                    Vector3f(0.9f, 0.1f, 0.6f),
                    AxisAngle4f(0f, 0f, 0f, 1f)
                )
                
                // Chains at corners
                for (i in 0..1) {
                    for (j in 0..1) {
                        val chain = world.spawn(
                            location.clone().add(0.2 + i * 0.6, 0.2, 0.2 + j * 0.6), 
                            BlockDisplay::class.java
                        )
                        chain.block = Bukkit.createBlockData(Material.CHAIN)
                        chain.transformation = Transformation(
                            Vector3f(0f, 0f, 0f),
                            AxisAngle4f(0f, 0f, 0f, 1f),
                            Vector3f(0.15f, 0.9f, 0.15f),
                            AxisAngle4f(0f, 0f, 0f, 1f)
                        )
                    }
                }
            }
            
            "rare" -> {
                // Rails around the chest
                val rail = world.spawn(location.clone().add(0.5, 0.6, 0.5), ItemDisplay::class.java)
                rail.setItemStack(ItemStack(Material.RAIL))
                rail.transformation = Transformation(
                    Vector3f(0f, 0f, 0f),
                    AxisAngle4f(0f, 0f, 0f, 1f),
                    Vector3f(1.2f, 0.1f, 1.2f),
                    AxisAngle4f(0f, 0f, 0f, 1f)
                )
                
                // Iron ingot accent
                val iron = world.spawn(location.clone().add(0.5, 0.8, 0.5), ItemDisplay::class.java)
                iron.setItemStack(ItemStack(Material.IRON_INGOT))
                iron.transformation = Transformation(
                    Vector3f(0f, 0f, 0f),
                    AxisAngle4f(0f, 0f, 0f, 1f),
                    Vector3f(0.5f, 0.5f, 0.5f),
                    AxisAngle4f(0f, 0f, 0f, 1f)
                )
            }
            
            "epic" -> {
                // Amethyst clusters at corners
                for (i in 0..1) {
                    for (j in 0..1) {
                        val amethyst = world.spawn(
                            location.clone().add(0.2 + i * 0.6, 0.7, 0.2 + j * 0.6), 
                            BlockDisplay::class.java
                        )
                        amethyst.block = Bukkit.createBlockData(Material.AMETHYST_CLUSTER)
                        amethyst.transformation = Transformation(
                            Vector3f(0f, 0f, 0f),
                            AxisAngle4f(0f, 0f, 0f, 1f),
                            Vector3f(0.3f, 0.3f, 0.3f),
                            AxisAngle4f(0f, 0f, 0f, 1f)
                        )
                    }
                }
                
                // Gold ingot accent
                val gold = world.spawn(location.clone().add(0.5, 0.9, 0.5), ItemDisplay::class.java)
                gold.setItemStack(ItemStack(Material.GOLD_INGOT))
                gold.transformation = Transformation(
                    Vector3f(0f, 0f, 0f),
                    AxisAngle4f(0f, 0f, 0f, 1f),
                    Vector3f(0.6f, 0.6f, 0.6f),
                    AxisAngle4f(0f, 0f, 0f, 1f)
                )
            }
            
            "mythic" -> {
                // Obsidian core
                val obsidian = world.spawn(location.clone().add(0.5, 0.3, 0.5), BlockDisplay::class.java)
                obsidian.block = Bukkit.createBlockData(Material.OBSIDIAN)
                obsidian.transformation = Transformation(
                    Vector3f(0f, 0f, 0f),
                    AxisAngle4f(0f, 0f, 0f, 1f),
                    Vector3f(0.6f, 0.4f, 0.6f),
                    AxisAngle4f(0f, 0f, 0f, 1f)
                )
                
                // Netherite scrap accents
                val netherite = world.spawn(location.clone().add(0.5, 1.0, 0.5), ItemDisplay::class.java)
                netherite.setItemStack(ItemStack(Material.NETHERITE_SCRAP))
                netherite.transformation = Transformation(
                    Vector3f(0f, 0f, 0f),
                    AxisAngle4f(0f, 0f, 0f, 1f),
                    Vector3f(0.7f, 0.7f, 0.7f),
                    AxisAngle4f(0f, 0f, 0f, 1f)
                )
            }
        }
        
        // Add text badge
        val text = world.spawn(location.clone().add(0.5, 1.2, 0.3), TextDisplay::class.java)
        val colorCode = when (tier.lowercase()) {
            "common" -> "§7"
            "rare" -> "§9"
            "epic" -> "§d"
            "mythic" -> "§6"
            else -> "§f"
        }
        text.text(Component.text("${colorCode}${tier.uppercase()}"))
        text.transformation = Transformation(
            Vector3f(0f, 0f, 0f),
            AxisAngle4f(0f, 0f, 0f, 1f),
            Vector3f(0.5f, 0.5f, 0.5f),
            AxisAngle4f(0f, 0f, 0f, 1f)
        )
        
        plugin.logger.info("Created visual shell for $tier chest")
    }
    
    private fun generateLoot(player: org.bukkit.entity.Player, tier: String, location: Location) {
        val loot = when (tier.lowercase()) {
            "common" -> listOf(
                ItemStack(Material.IRON_INGOT, 2),
                ItemStack(Material.BREAD, 5),
                ItemStack(Material.COAL, 8)
            )
            "rare" -> listOf(
                ItemStack(Material.DIAMOND, 1),
                ItemStack(Material.GOLD_INGOT, 3),
                ItemStack(Material.ENCHANTED_BOOK, 1)
            )
            "epic" -> listOf(
                ItemStack(Material.DIAMOND, 3),
                ItemStack(Material.NETHERITE_INGOT, 1),
                ItemStack(Material.TOTEM_OF_UNDYING, 1)
            )
            "mythic" -> listOf(
                ItemStack(Material.NETHERITE_INGOT, 2),
                ItemStack(Material.NETHER_STAR, 1),
                ItemStack(Material.ELYTRA, 1)
            )
            else -> listOf(ItemStack(Material.DIRT, 1))
        }
        
        for (item in loot) {
            player.inventory.addItem(item)
        }
        
        plugin.logger.info("Generated $tier loot for ${player.name}")
    }
}
