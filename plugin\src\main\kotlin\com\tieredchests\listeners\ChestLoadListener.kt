package com.tieredchests.listeners

import com.tieredchests.TieredChestsPlugin
import org.bukkit.event.EventHandler
import org.bukkit.event.Listener
import org.bukkit.event.world.ChunkLoadEvent

class ChestLoadListener(private val plugin: TieredChestsPlugin) : Listener {
    
    @EventHandler
    fun onChunkLoad(event: ChunkLoadEvent) {
        // Rebuild shells for chests in this chunk
        plugin.server.scheduler.runTaskLater(plugin, Runnable {
            for (chest in plugin.chestManager.getAllChests()) {
                if (chest.location.chunk == event.chunk) {
                    // Check if shell entities exist
                    val allExist = chest.shellEntities.all { uuid ->
                        chest.location.world.getEntity(uuid) != null
                    }
                    
                    if (!allExist) {
                        // Rebuild shell
                        val tier = plugin.tierManager.getTier(chest.tierId)
                        if (tier != null) {
                            plugin.shellManager.rebuildShell(chest, tier)
                            plugin.chestManager.saveChest(chest)
                        }
                    }
                }
            }
        }, 20L) // Wait 1 second after chunk load
    }
}

