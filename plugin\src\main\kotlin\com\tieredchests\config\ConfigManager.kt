package com.tieredchests.config

import com.tieredchests.TieredChestsPlugin
import org.bukkit.configuration.file.YamlConfiguration
import java.io.File
import java.util.logging.Level

class ConfigManager(private val plugin: TieredChestsPlugin) {
    
    private val configFile = File(plugin.dataFolder, "config.yml")
    private val tiersFile = File(plugin.dataFolder, "tiers.yml")
    private val lootTablesFile = File(plugin.dataFolder, "loot_tables.yml")
    private val chestsDir = File(plugin.dataFolder, "chests")
    
    lateinit var config: YamlConfiguration
        private set
    
    lateinit var tiersConfig: YamlConfiguration
        private set
    
    lateinit var lootTablesConfig: YamlConfiguration
        private set
    
    fun loadAll() {
        // Create data folder
        if (!plugin.dataFolder.exists()) {
            plugin.dataFolder.mkdirs()
        }
        
        if (!chestsDir.exists()) {
            chestsDir.mkdirs()
        }
        
        // Load or create configs
        config = loadOrCreate(configFile, "config.yml")
        tiersConfig = loadOrCreate(tiersFile, "tiers.yml")
        lootTablesConfig = loadOrCreate(lootTablesFile, "loot_tables.yml")
        
        // Migrate if needed
        migrateConfigs()
    }
    
    private fun loadOrCreate(file: File, resourceName: String): YamlConfiguration {
        if (!file.exists()) {
            plugin.saveResource(resourceName, false)
        }
        
        return try {
            YamlConfiguration.loadConfiguration(file)
        } catch (e: Exception) {
            plugin.logger.log(Level.SEVERE, "Failed to load ${file.name}", e)
            YamlConfiguration()
        }
    }
    
    private fun migrateConfigs() {
        // Check config version and migrate if needed
        val currentVersion = config.getInt("configVersion", 1)
        val targetVersion = 1
        
        if (currentVersion < targetVersion) {
            plugin.logger.info("Migrating config from v$currentVersion to v$targetVersion")
            // Add migration logic here
            config.set("configVersion", targetVersion)
            saveConfig()
        }
    }
    
    fun saveConfig() {
        try {
            config.save(configFile)
        } catch (e: Exception) {
            plugin.logger.log(Level.SEVERE, "Failed to save config.yml", e)
        }
    }
    
    fun saveTiersConfig() {
        try {
            tiersConfig.save(tiersFile)
        } catch (e: Exception) {
            plugin.logger.log(Level.SEVERE, "Failed to save tiers.yml", e)
        }
    }
    
    fun saveLootTablesConfig() {
        try {
            lootTablesConfig.save(lootTablesFile)
        } catch (e: Exception) {
            plugin.logger.log(Level.SEVERE, "Failed to save loot_tables.yml", e)
        }
    }
    
    fun getChestsDir(): File = chestsDir
}

