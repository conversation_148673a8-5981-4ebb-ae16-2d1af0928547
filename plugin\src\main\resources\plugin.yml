name: CustomTieredChests
version: ${project.version}
main: com.tieredchests.TieredChestsPlugin
api-version: '1.20'
folia-supported: true
author: TieredChests
description: Custom Tiered Chests with Display Entity visuals
website: https://github.com/tieredchests

softdepend:
  - PlaceholderAPI
  - WorldGuard
  - Vault

commands:
  ctc:
    description: Main command for Custom Tiered Chests
    usage: /ctc <subcommand>
    aliases: [tieredchests, tchests]

permissions:
  ctc.admin:
    description: Full admin access
    default: op
    children:
      ctc.admin.givechest: true
      ctc.admin.givekey: true
      ctc.admin.setloot: true
      ctc.admin.inspect: true
      ctc.admin.badge: true
      ctc.admin.rebuild: true
      ctc.admin.reload: true
  ctc.admin.givechest:
    description: Give tiered chests
    default: op
  ctc.admin.givekey:
    description: Give keys
    default: op
  ctc.admin.setloot:
    description: Set loot tables
    default: op
  ctc.admin.inspect:
    description: Inspect chests
    default: op
  ctc.admin.badge:
    description: Toggle badges
    default: op
  ctc.admin.rebuild:
    description: Rebuild shells
    default: op
  ctc.admin.reload:
    description: Reload configs
    default: op
  ctc.use:
    description: Use tiered chests
    default: true
  ctc.place:
    description: Place tiered chests
    default: true
  ctc.break:
    description: Break tiered chests
    default: true

