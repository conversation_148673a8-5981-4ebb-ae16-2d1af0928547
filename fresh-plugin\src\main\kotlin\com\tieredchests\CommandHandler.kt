package com.tieredchests

import net.kyori.adventure.text.Component
import net.kyori.adventure.text.format.NamedTextColor
import org.bukkit.Bukkit
import org.bukkit.Material
import org.bukkit.NamespacedKey
import org.bukkit.command.Command
import org.bukkit.command.CommandExecutor
import org.bukkit.command.CommandSender
import org.bukkit.command.TabCompleter
import org.bukkit.entity.Player
import org.bukkit.inventory.ItemStack
import org.bukkit.persistence.PersistentDataType

class CommandHandler(private val plugin: TieredChestsPlugin) : CommandExecutor, TabCompleter {
    
    private val chestKey = NamespacedKey(plugin, "tiered_chest")
    private val tierKey = NamespacedKey(plugin, "tier")

    private fun getColorCode(color: NamedTextColor): String {
        return when (color) {
            NamedTextColor.GRAY -> "7"
            NamedTextColor.BLUE -> "9"
            NamedTextColor.LIGHT_PURPLE -> "d"
            NamedTextColor.GOLD -> "6"
            else -> "f"
        }
    }
    
    override fun onCommand(sender: CommandSender, command: Command, label: String, args: Array<out String>): Boolean {
        plugin.logger.info("COMMAND RECEIVED: ${sender.name} -> ${args.joinToString(" ")}")
        sender.sendMessage("§a[DEBUG] Command received: ${args.joinToString(" ")}")
        
        if (args.isEmpty()) {
            sendHelp(sender)
            return true
        }
        
        when (args[0].lowercase()) {
            "givechest" -> handleGiveChest(sender, args)
            "givekey" -> handleGiveKey(sender, args)
            "test" -> handleTest(sender)
            else -> sendHelp(sender)
        }
        
        return true
    }
    
    private fun handleTest(sender: CommandSender) {
        sender.sendMessage("§a✓ Plugin is working!")
        plugin.logger.info("Test command executed by ${sender.name}")
    }
    
    private fun handleGiveChest(sender: CommandSender, args: Array<out String>) {
        if (!sender.hasPermission("ctc.admin")) {
            sender.sendMessage("§cNo permission!")
            return
        }
        
        if (args.size < 3) {
            sender.sendMessage("§cUsage: /ctc givechest <player> <tier>")
            return
        }
        
        val targetName = args[1]
        val tier = args[2]
        val player = Bukkit.getPlayer(targetName)
        
        if (player == null) {
            sender.sendMessage("§cPlayer '$targetName' not found!")
            return
        }
        
        // Create chest item
        val item = ItemStack(Material.BARREL)
        val meta = item.itemMeta
        
        val tierColor = when (tier.lowercase()) {
            "common" -> NamedTextColor.GRAY
            "rare" -> NamedTextColor.BLUE
            "epic" -> NamedTextColor.LIGHT_PURPLE
            "mythic" -> NamedTextColor.GOLD
            else -> NamedTextColor.WHITE
        }
        
        meta.setDisplayName("§${getColorCode(tierColor)}${tier.replaceFirstChar { it.uppercase() }} Chest")
        meta.lore = listOf(
            "§7Tier: $tier",
            "§eRight-click to place"
        )
        
        // Mark as tiered chest
        meta.persistentDataContainer.set(chestKey, PersistentDataType.BOOLEAN, true)
        meta.persistentDataContainer.set(tierKey, PersistentDataType.STRING, tier)
        
        item.itemMeta = meta
        
        player.inventory.addItem(item)
        sender.sendMessage("§aGave ${player.name} a $tier chest!")
        plugin.logger.info("Gave ${player.name} a $tier chest")
    }
    
    private fun handleGiveKey(sender: CommandSender, args: Array<out String>) {
        if (!sender.hasPermission("ctc.admin")) {
            sender.sendMessage("§cNo permission!")
            return
        }
        
        if (args.size < 3) {
            sender.sendMessage("§cUsage: /ctc givekey <player> <tier>")
            return
        }
        
        val targetName = args[1]
        val tier = args[2]
        val player = Bukkit.getPlayer(targetName)
        
        if (player == null) {
            sender.sendMessage("§cPlayer '$targetName' not found!")
            return
        }
        
        // Create key item
        val item = ItemStack(Material.TRIPWIRE_HOOK)
        val meta = item.itemMeta
        
        meta.setDisplayName("§6${tier.replaceFirstChar { it.uppercase() }} Key")
        meta.lore = listOf(
            "§7Tier: $tier",
            "§eRight-click chest to open"
        )
        
        // Mark as key
        meta.persistentDataContainer.set(NamespacedKey(plugin, "key"), PersistentDataType.BOOLEAN, true)
        meta.persistentDataContainer.set(tierKey, PersistentDataType.STRING, tier)
        
        item.itemMeta = meta
        
        player.inventory.addItem(item)
        sender.sendMessage("§aGave ${player.name} a $tier key!")
        plugin.logger.info("Gave ${player.name} a $tier key")
    }
    
    private fun sendHelp(sender: CommandSender) {
        sender.sendMessage("§e=== Tiered Chests Fresh ===")
        sender.sendMessage("§7/ctc test - Test if plugin works")
        sender.sendMessage("§7/ctc givechest <player> <tier> - Give chest")
        sender.sendMessage("§7/ctc givekey <player> <tier> - Give key")
        sender.sendMessage("§7Tiers: common, rare, epic, mythic")
    }
    
    override fun onTabComplete(sender: CommandSender, command: Command, alias: String, args: Array<out String>): List<String> {
        return when (args.size) {
            1 -> listOf("test", "givechest", "givekey").filter { it.startsWith(args[0].lowercase()) }
            2 -> if (args[0].lowercase() in listOf("givechest", "givekey")) {
                Bukkit.getOnlinePlayers().map { it.name }.filter { it.startsWith(args[1], true) }
            } else emptyList()
            3 -> if (args[0].lowercase() in listOf("givechest", "givekey")) {
                listOf("common", "rare", "epic", "mythic").filter { it.startsWith(args[2], true) }
            } else emptyList()
            else -> emptyList()
        }
    }
}
