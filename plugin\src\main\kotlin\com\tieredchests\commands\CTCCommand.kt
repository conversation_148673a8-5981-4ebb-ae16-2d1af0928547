package com.tieredchests.commands

import com.tieredchests.TieredChestsPlugin
import com.tieredchests.model.KeyBindMode
import net.kyori.adventure.text.Component
import net.kyori.adventure.text.format.NamedTextColor
import org.bukkit.Bukkit
import org.bukkit.Material
import org.bukkit.NamespacedKey
import org.bukkit.command.Command
import org.bukkit.command.CommandExecutor
import org.bukkit.command.CommandSender
import org.bukkit.command.TabCompleter
import org.bukkit.entity.Player
import org.bukkit.inventory.ItemStack
import org.bukkit.persistence.PersistentDataType
import java.util.*

class CTCCommand(private val plugin: TieredChestsPlugin) : CommandExecutor, TabCompleter {
    
    private val chestItemKey = NamespacedKey(plugin, "chest_item")
    private val tierIdKey = NamespacedKey(plugin, "tier_id")
    
    override fun onCommand(sender: CommandSender, command: Command, label: String, args: Array<out String>): Boolean {
        if (args.isEmpty()) {
            sendHelp(sender)
            return true
        }
        
        when (args[0].lowercase()) {
            "givechest" -> handleGiveChest(sender, args)
            "givekey" -> handleGiveKey(sender, args)
            "setloottable", "setloot" -> handleSetLoot(sender, args)
            "inspect" -> handleInspect(sender, args)
            "togglebadge" -> handleToggleBadge(sender, args)
            "rebuildshell", "rebuild" -> handleRebuildShell(sender, args)
            "reload" -> handleReload(sender, args)
            else -> sendHelp(sender)
        }
        
        return true
    }
    
    private fun handleGiveChest(sender: CommandSender, args: Array<out String>) {
        if (!sender.hasPermission("ctc.admin.givechest")) {
            sender.sendMessage("§cNo permission!")
            return
        }
        
        if (args.size < 3) {
            sender.sendMessage("§cUsage: /ctc givechest <player> <tier> [amount]")
            return
        }
        
        val player = Bukkit.getPlayer(args[1])
        if (player == null) {
            sender.sendMessage("§cPlayer not found!")
            return
        }
        
        val tierId = args[2]
        val tier = plugin.tierManager.getTier(tierId)
        if (tier == null) {
            sender.sendMessage("§cInvalid tier: $tierId")
            return
        }
        
        val amount = args.getOrNull(3)?.toIntOrNull() ?: 1
        
        // Create chest item
        val item = ItemStack(tier.coreBlock, amount)
        val meta = item.itemMeta
        
        meta.displayName(Component.text("${tier.displayName} Chest").color(NamedTextColor.GOLD))
        meta.lore(listOf(
            Component.text("Tier: ${tier.displayName}").color(NamedTextColor.GRAY),
            Component.text("Place to create a tiered chest").color(NamedTextColor.YELLOW)
        ))
        
        meta.persistentDataContainer.set(chestItemKey, PersistentDataType.BOOLEAN, true)
        meta.persistentDataContainer.set(tierIdKey, PersistentDataType.STRING, tierId)
        
        item.itemMeta = meta
        
        player.inventory.addItem(item)
        sender.sendMessage("§aGave ${player.name} ${amount}x ${tier.displayName} Chest")
    }
    
    private fun handleGiveKey(sender: CommandSender, args: Array<out String>) {
        if (!sender.hasPermission("ctc.admin.givekey")) {
            sender.sendMessage("§cNo permission!")
            return
        }
        
        if (args.size < 3) {
            sender.sendMessage("§cUsage: /ctc givekey <player> <tier> [uses] [bind:tier|instance]")
            return
        }
        
        val player = Bukkit.getPlayer(args[1])
        if (player == null) {
            sender.sendMessage("§cPlayer not found!")
            return
        }
        
        val tierId = args[2]
        val tier = plugin.tierManager.getTier(tierId)
        if (tier == null) {
            sender.sendMessage("§cInvalid tier: $tierId")
            return
        }
        
        val uses = args.getOrNull(3)?.toIntOrNull()
        val bindMode = when (args.getOrNull(4)?.lowercase()) {
            "instance" -> KeyBindMode.INSTANCE
            else -> KeyBindMode.TIER
        }
        
        val key = plugin.keyManager.createKey(tierId, uses, bindMode, null)
        player.inventory.addItem(key)
        
        sender.sendMessage("§aGave ${player.name} a ${tier.displayName} Key")
    }
    
    private fun handleSetLoot(sender: CommandSender, args: Array<out String>) {
        if (!sender.hasPermission("ctc.admin.setloot")) {
            sender.sendMessage("§cNo permission!")
            return
        }
        
        if (sender !is Player) {
            sender.sendMessage("§cOnly players can use this command!")
            return
        }
        
        if (args.size < 2) {
            sender.sendMessage("§cUsage: /ctc setloot <lootTableId>")
            return
        }
        
        val lootTableId = args[1]
        
        val block = sender.getTargetBlockExact(5)
        if (block == null) {
            sender.sendMessage("§cLook at a chest!")
            return
        }
        
        val chest = plugin.chestManager.getChestAt(block.location)
        if (chest == null) {
            sender.sendMessage("§cNot a tiered chest!")
            return
        }

        val updatedChest = chest.copy(lootTableId = lootTableId)
        plugin.chestManager.saveChest(updatedChest)
        
        sender.sendMessage("§aLoot table set to: $lootTableId")
    }
    
    private fun handleInspect(sender: CommandSender, args: Array<out String>) {
        if (!sender.hasPermission("ctc.admin.inspect")) {
            sender.sendMessage("§cNo permission!")
            return
        }
        
        if (sender !is Player) {
            sender.sendMessage("§cOnly players can use this command!")
            return
        }
        
        val block = sender.getTargetBlockExact(5)
        if (block == null) {
            sender.sendMessage("§cLook at a chest!")
            return
        }
        
        val chest = plugin.chestManager.getChestAt(block.location)
        if (chest == null) {
            sender.sendMessage("§cNot a tiered chest!")
            return
        }
        
        sender.sendMessage("§e=== Chest Info ===")
        sender.sendMessage("§7UUID: §f${chest.uuid}")
        sender.sendMessage("§7Tier: §f${chest.tierId}")
        sender.sendMessage("§7Loot Table: §f${chest.lootTableId}")
        sender.sendMessage("§7Owner: §f${chest.owner}")
        sender.sendMessage("§7Ready: §f${chest.isReady()}")
        if (!chest.isReady()) {
            val remaining = chest.getRemainingCooldown()
            sender.sendMessage("§7Cooldown: §f${remaining / 60000}m ${(remaining % 60000) / 1000}s")
        }
    }
    
    private fun handleToggleBadge(sender: CommandSender, args: Array<out String>) {
        if (!sender.hasPermission("ctc.admin.badge")) {
            sender.sendMessage("§cNo permission!")
            return
        }
        
        if (sender !is Player) {
            sender.sendMessage("§cOnly players can use this command!")
            return
        }
        
        val block = sender.getTargetBlockExact(5)
        if (block == null) {
            sender.sendMessage("§cLook at a chest!")
            return
        }
        
        val chest = plugin.chestManager.getChestAt(block.location)
        if (chest == null) {
            sender.sendMessage("§cNot a tiered chest!")
            return
        }
        
        chest.badgeEnabled = !chest.badgeEnabled
        plugin.chestManager.saveChest(chest)
        
        val tier = plugin.tierManager.getTier(chest.tierId)
        if (tier != null) {
            plugin.shellManager.rebuildShell(chest, tier)
        }
        
        sender.sendMessage("§aBadge ${if (chest.badgeEnabled) "enabled" else "disabled"}")
    }
    
    private fun handleRebuildShell(sender: CommandSender, args: Array<out String>) {
        if (!sender.hasPermission("ctc.admin.rebuild")) {
            sender.sendMessage("§cNo permission!")
            return
        }
        
        if (sender !is Player) {
            sender.sendMessage("§cOnly players can use this command!")
            return
        }
        
        val block = sender.getTargetBlockExact(5)
        if (block == null) {
            sender.sendMessage("§cLook at a chest!")
            return
        }
        
        plugin.chestManager.rebuildShellAt(block.location)
        sender.sendMessage("§aShell rebuilt!")
    }
    
    private fun handleReload(sender: CommandSender, args: Array<out String>) {
        if (!sender.hasPermission("ctc.admin.reload")) {
            sender.sendMessage("§cNo permission!")
            return
        }
        
        try {
            plugin.reload()
            sender.sendMessage("§aConfiguration reloaded!")
        } catch (e: Exception) {
            sender.sendMessage("§cFailed to reload: ${e.message}")
        }
    }
    
    private fun sendHelp(sender: CommandSender) {
        sender.sendMessage("§e=== Custom Tiered Chests ===")
        sender.sendMessage("§7/ctc givechest <player> <tier> [amount]")
        sender.sendMessage("§7/ctc givekey <player> <tier> [uses] [bind]")
        sender.sendMessage("§7/ctc setloot <lootTableId>")
        sender.sendMessage("§7/ctc inspect")
        sender.sendMessage("§7/ctc togglebadge")
        sender.sendMessage("§7/ctc rebuild")
        sender.sendMessage("§7/ctc reload")
    }
    
    override fun onTabComplete(sender: CommandSender, command: Command, alias: String, args: Array<out String>): List<String> {
        return when (args.size) {
            1 -> listOf("givechest", "givekey", "setloot", "inspect", "togglebadge", "rebuild", "reload")
                .filter { it.startsWith(args[0].lowercase()) }
            2 -> when (args[0].lowercase()) {
                "givechest", "givekey" -> Bukkit.getOnlinePlayers().map { it.name }
                else -> emptyList()
            }
            3 -> when (args[0].lowercase()) {
                "givechest", "givekey" -> plugin.tierManager.getTiers().map { it.id }
                else -> emptyList()
            }
            else -> emptyList()
        }
    }
}

